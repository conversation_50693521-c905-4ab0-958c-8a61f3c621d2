/**
 * Email Marketing Application
 * A complete Node.js/Electron implementation of the email marketing tool
 */

const { app, BrowserWindow, ipcMain, dialog, shell } = require('electron');
const fs = require('fs');
const path = require('path');
const { google } = require('googleapis');
const { OAuth2Client } = require('google-auth-library');
const crypto = require('crypto');
const express = require('express');
const http = require('http');
const url = require('url');

// ============================================================
// Constants & Global Variables
// ============================================================

// Application directories
const USER_DATA_PATH = app.getPath('userData');
const ACCOUNT_FILE = path.join(USER_DATA_PATH, 'accounts.txt');
const LISTS_DIR = path.join(USER_DATA_PATH, 'lists');
const TOKENS_DIR = path.join(USER_DATA_PATH, 'tokens');
const HISTORY_DIR = path.join(USER_DATA_PATH, 'send_history');
const CLIENTS_DIR = path.join(USER_DATA_PATH, 'clients');
const TRACKING_DIR = path.join(USER_DATA_PATH, 'tracking');
const SCOPES = ['https://www.googleapis.com/auth/gmail.send'];

// Global email sending variables
let sendingProcess = null;
let pauseSending = true;
let stopSending = false;

// Main window reference
let mainWindow;

// Tracking data
const trackingData = {
  opens: {
    total: 0,
    unique: 0,
    byEmail: {}, // Key: email, value: {count, lastOpened}
    recentActivity: [] // Array of {email, time, device, ip}
  },
  clicks: {
    total: 0,
    unique: 0,
    byEmail: {}, // Key: email, value: {count, lastClicked, links: {}}
    byLink: {}, // Key: link, value: {count, uniqueCount, lastClicked}
    recentActivity: [] // Array of {email, link, time, device}
  },
  campaigns: {} // Key: campaignId, value: {total, opens, clicks}
};

// Global variables for tracking system
const trackingApp = express();
const PORT = process.env.TRACKING_PORT || 3000;
let trackingServer = null;

// Initialize Express app for tracking
// These will be declared just once at the top level

// Parse URL-encoded bodies
trackingApp.use(express.urlencoded({ extended: true }));

// Track email opens
trackingApp.get('/track/open', (req, res) => {
  const { email, campaign } = req.query;

  if (!email) {
    res.status(400).send('Missing required parameters');
    return;
  }

  // Log the open event
  console.log(`Tracking: Email opened by ${email}, campaign: ${campaign || 'unknown'}`);

  // Get device information from user agent
  const userAgent = req.headers['user-agent'] || 'Unknown';
  const device = userAgent.includes('Mobile') ? 'Mobile' : 'Desktop';

  // Get IP address
  const ip = req.headers['x-forwarded-for'] ||
             req.connection.remoteAddress ||
             'Unknown';

  // Create tracking data object
  const trackingInfo = {
    email,
    campaign,
    device,
    ip,
    time: new Date()
  };

  // Send to renderer process
  if (mainWindow) {
    mainWindow.webContents.send('tracking-event', {
      type: 'open',
      data: trackingInfo
    });
  }

  // Update tracking data
  try {
    // Increment total opens
    trackingData.opens.total++;

    // Update unique opens
    if (!trackingData.opens.byEmail[email]) {
      trackingData.opens.unique++;
      trackingData.opens.byEmail[email] = {
        count: 0,
        lastOpened: new Date()
      };
    }

    // Update email specific data
    trackingData.opens.byEmail[email].count++;
    trackingData.opens.byEmail[email].lastOpened = new Date();

    // Add to recent activity
    trackingData.opens.recentActivity.unshift({
      email,
      time: new Date(),
      device,
      ip
    });

    // Limit recent activity list
    if (trackingData.opens.recentActivity.length > 100) {
      trackingData.opens.recentActivity.pop();
    }

    // Update campaign stats
    if (campaign) {
      if (!trackingData.campaigns[campaign]) {
        trackingData.campaigns[campaign] = {
          total: 0,
          opens: 0,
          clicks: 0,
          unique_opens: 0,
          unique_clicks: 0,
          recipients: {}
        };
      }

      trackingData.campaigns[campaign].opens++;

      if (!trackingData.campaigns[campaign].recipients[email]) {
        trackingData.campaigns[campaign].recipients[email] = {
          opens: 0,
          clicks: 0
        };
        trackingData.campaigns[campaign].unique_opens++;
      }

      trackingData.campaigns[campaign].recipients[email].opens++;
    }

    // Save tracking data periodically
    saveTrackingData();
  } catch (error) {
    console.error('Error updating tracking data:', error);
  }

  // Return a transparent 1x1 GIF
  const transparentGif = Buffer.from(
    'R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7',
    'base64'
  );
  res.setHeader('Content-Type', 'image/gif');
  res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
  res.end(transparentGif);
});

// Track link clicks
trackingApp.get('/track/click', (req, res) => {
  const { email, campaign, url } = req.query;

  if (!email || !url) {
    res.status(400).send('Missing required parameters');
    return;
  }

  // Log the click event
  console.log(`Tracking: Link clicked by ${email}, campaign: ${campaign || 'unknown'}, url: ${url}`);

  // Get device information from user agent
  const userAgent = req.headers['user-agent'] || 'Unknown';
  const device = userAgent.includes('Mobile') ? 'Mobile' : 'Desktop';

  // Create tracking data object
  const trackingInfo = {
    email,
    campaign,
    url,
    device,
    time: new Date()
  };

  // Send to renderer process
  if (mainWindow) {
    mainWindow.webContents.send('tracking-event', {
      type: 'click',
      data: trackingInfo
    });
  }

  // Update tracking data
  try {
    // Increment total clicks
    trackingData.clicks.total++;

    // Update unique clicks
    if (!trackingData.clicks.byEmail[email]) {
      trackingData.clicks.unique++;
      trackingData.clicks.byEmail[email] = {
        count: 0,
        lastClicked: new Date(),
        links: {}
      };
    }

    // Update email specific data
    trackingData.clicks.byEmail[email].count++;
    trackingData.clicks.byEmail[email].lastClicked = new Date();

    // Update link specific data for this email
    if (!trackingData.clicks.byEmail[email].links[url]) {
      trackingData.clicks.byEmail[email].links[url] = 0;
    }
    trackingData.clicks.byEmail[email].links[url]++;

    // Update link specific totals
    if (!trackingData.clicks.byLink[url]) {
      trackingData.clicks.byLink[url] = {
        count: 0,
        uniqueCount: 0,
        lastClicked: new Date()
      };
    }

    // Check if this is the first time this user clicked this link
    const isFirstClick = !trackingData.clicks.byEmail[email].links[url] ||
                         trackingData.clicks.byEmail[email].links[url] === 1;

    // Update link data
    trackingData.clicks.byLink[url].count++;
    if (isFirstClick) {
      trackingData.clicks.byLink[url].uniqueCount++;
    }
    trackingData.clicks.byLink[url].lastClicked = new Date();

    // Add to recent activity
    trackingData.clicks.recentActivity.unshift({
      email,
      link: url,
      time: new Date(),
      device
    });

    // Limit recent activity list
    if (trackingData.clicks.recentActivity.length > 100) {
      trackingData.clicks.recentActivity.pop();
    }

    // Update campaign stats
    if (campaign) {
      if (!trackingData.campaigns[campaign]) {
        trackingData.campaigns[campaign] = {
          total: 0,
          opens: 0,
          clicks: 0,
          unique_opens: 0,
          unique_clicks: 0,
          recipients: {}
        };
      }

      trackingData.campaigns[campaign].clicks++;

      if (!trackingData.campaigns[campaign].recipients[email]) {
        trackingData.campaigns[campaign].recipients[email] = {
          opens: 0,
          clicks: 0
        };
      }

      if (trackingData.campaigns[campaign].recipients[email].clicks === 0) {
        trackingData.campaigns[campaign].unique_clicks++;
      }

      trackingData.campaigns[campaign].recipients[email].clicks++;
    }

    // Save tracking data periodically
    saveTrackingData();
  } catch (error) {
    console.error('Error updating tracking data:', error);
  }

  // Redirect to the original URL
  res.redirect(url);
});

// ============================================================
// Utility Functions
// ============================================================

// Create necessary directories
function ensureDirectoriesExist() {
  [LISTS_DIR, TOKENS_DIR, HISTORY_DIR, CLIENTS_DIR, TRACKING_DIR].forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });
}

// Save tracking data to file
function saveTrackingData() {
  try {
    const trackingFile = path.join(TRACKING_DIR, 'tracking-data.json');
    fs.writeFileSync(trackingFile, JSON.stringify(trackingData, null, 2));
    return true;
  } catch (error) {
    console.error('Error saving tracking data:', error);
    return false;
  }
}

// Load tracking data from file
function loadTrackingData() {
  try {
    const trackingFile = path.join(TRACKING_DIR, 'tracking-data.json');
    if (fs.existsSync(trackingFile)) {
      const data = fs.readFileSync(trackingFile, 'utf8');
      const parsed = JSON.parse(data);

      // Copy data to our tracking object while preserving structure
      Object.assign(trackingData.opens, parsed.opens || {});
      Object.assign(trackingData.clicks, parsed.clicks || {});
      Object.assign(trackingData.campaigns, parsed.campaigns || {});

      console.log('Loaded tracking data:',
        `${trackingData.opens.total} opens, ${trackingData.clicks.total} clicks`);
      return true;
    }
  } catch (error) {
    console.error('Error loading tracking data:', error);
  }
  return false;
}

// ============================================================
// Email Tracking Helper Functions
// ============================================================

/**
 * Generate a tracking pixel for an email
 * @param {string} email - Recipient's email address
 * @param {string} campaignId - Campaign identifier
 * @returns {string} HTML for the tracking pixel
 */
function generateTrackingPixel(email, campaignId) {
  const host = process.env.TRACKING_HOST || 'localhost:3000';
  const protocol = host.startsWith('localhost') ? 'http' : 'https';
  const pixelUrl = `${protocol}://${host}/track/open?email=${encodeURIComponent(email)}&campaign=${encodeURIComponent(campaignId)}&t=${Date.now()}`;

  return `<img src="${pixelUrl}" width="1" height="1" alt="" style="display:none;">`;
}

/**
 * Process HTML body to add tracking for all links
 * @param {string} html - Original HTML content
 * @param {string} email - Recipient's email address
 * @param {string} campaignId - Campaign identifier
 * @returns {string} HTML with tracked links
 */
function processLinksForTracking(html, email, campaignId) {
  const host = process.env.TRACKING_HOST || 'localhost:3000';
  const protocol = host.startsWith('localhost') ? 'http' : 'https';

  // Simple HTML processing with regex
  // For a production app, a more robust HTML parser like cheerio would be better
  return html.replace(/<a\s+(?:[^>]*?\s+)?href=["']([^"']*)["']([^>]*)>/gi, (match, url, rest) => {
    // Skip empty urls, anchors, javascript links, and mailto links
    if (!url || url.startsWith('#') || url.startsWith('javascript:') || url.startsWith('mailto:')) {
      return match;
    }

    // Create tracking URL
    const trackingUrl = `${protocol}://${host}/track/click?email=${encodeURIComponent(email)}&campaign=${encodeURIComponent(campaignId)}&url=${encodeURIComponent(url)}&t=${Date.now()}`;

    // Replace the URL
    return `<a href="${trackingUrl}"${rest}>`;
  });
}

/**
 * Add tracking to an email body
 * @param {string} html - Original HTML content
 * @param {string} email - Recipient's email address
 * @param {string} campaignId - Campaign identifier
 * @returns {string} HTML with tracking pixel and tracked links
 */
function addTrackingToEmail(html, email, campaignId) {
  if (!campaignId) {
    campaignId = 'campaign_' + Math.random().toString(36).substring(2, 15);
  }

  // Process HTML for link tracking
  let processed = processLinksForTracking(html, email, campaignId);

  // Add tracking pixel at the end of the email
  const trackingPixel = generateTrackingPixel(email, campaignId);

  // Check if the HTML has a closing body tag
  if (processed.indexOf('</body>') !== -1) {
    // Insert before closing body tag
    processed = processed.replace('</body>', trackingPixel + '</body>');
  } else {
    // Just append to the end
    processed += trackingPixel;
  }

  return processed;
}

// Replace template tags in text
function replaceTags(text, email) {
  // Basic tag replacements
  let replaced = text.replace(/\[random_string\]/g, crypto.randomBytes(4).toString('hex'));
  replaced = replaced.replace(/\[current_date\]/g, new Date().toISOString().split('T')[0]);

  // Handle [an_X] tags where X is a number for alphanumeric string of length X
  const anTagRegex = /\[an_(\d+)\]/g;
  replaced = replaced.replace(anTagRegex, (match, length) => {
    return generateRandomString(parseInt(length, 10));
  });

  // Handle [number_X] tags where X is the max value
  const numberTagRegex = /\[number_(\d+)\]/g;
  replaced = replaced.replace(numberTagRegex, (match, maxValue) => {
    return Math.floor(Math.random() * parseInt(maxValue, 10) + 1).toString();
  });

  return replaced;
}

// Generate random alphanumeric string of specified length
function generateRandomString(length) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// ============================================================
// Main Application Functions
// ============================================================

// Create the browser window
function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 900,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true
    }
  });

  mainWindow.loadFile('index.html');

  // Open DevTools in development
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
  }
}

// Initialize the application
app.whenReady().then(() => {
  // Create necessary directories
  ensureDirectoriesExist();

  // Load tracking data from disk
  loadTrackingData();

  // Start the tracking server
  startTrackingServer();

  // Create the main window
  createWindow();

  app.on('activate', function () {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit();
});

app.on('will-quit', function() {
  // Save tracking data before quitting
  saveTrackingData();

  // Stop the tracking server
  stopTrackingServer();
});

// ============================================================
// Account Management
// ============================================================

// Load all accounts
ipcMain.handle('load-accounts', async () => {
  try {
    if (!fs.existsSync(ACCOUNT_FILE)) {
      return [];
    }
    const data = fs.readFileSync(ACCOUNT_FILE, 'utf8');
    return data.split('\n').filter(account => account.trim());
  } catch (error) {
    console.error('Error loading accounts:', error);
    return [];
  }
});

// Save a new account
ipcMain.handle('save-account', async (event, account) => {
  try {
    if (!fs.existsSync(ACCOUNT_FILE)) {
      fs.writeFileSync(ACCOUNT_FILE, account);
    } else {
      const accounts = fs.readFileSync(ACCOUNT_FILE, 'utf8')
        .split('\n')
        .filter(acc => acc.trim());

      if (!accounts.includes(account)) {
        accounts.push(account);
        fs.writeFileSync(ACCOUNT_FILE, accounts.join('\n'));
      }
    }
    return true;
  } catch (error) {
    console.error('Error saving account:', error);
    return false;
  }
});

// Delete an account
ipcMain.handle('delete-account', async (event, account) => {
  try {
    if (fs.existsSync(ACCOUNT_FILE)) {
      const accounts = fs.readFileSync(ACCOUNT_FILE, 'utf8')
        .split('\n')
        .filter(acc => acc.trim() && acc !== account);
      fs.writeFileSync(ACCOUNT_FILE, accounts.join('\n'));

      // Also delete token file if it exists
      const tokenFile = path.join(TOKENS_DIR, `token_${account}.json`);
      if (fs.existsSync(tokenFile)) {
        fs.unlinkSync(tokenFile);
      }
    }
    return true;
  } catch (error) {
    console.error('Error deleting account:', error);
    return false;
  }
});

// ============================================================
// Email List Management
// ============================================================

// Load all email lists
ipcMain.handle('load-email-lists', async () => {
  try {
    if (!fs.existsSync(LISTS_DIR)) {
      fs.mkdirSync(LISTS_DIR, { recursive: true });
      return [];
    }

    const files = fs.readdirSync(LISTS_DIR);
    const lists = [];

    for (const file of files) {
      if (file.endsWith('.txt')) {
        const filePath = path.join(LISTS_DIR, file);
        const data = fs.readFileSync(filePath, 'utf8');
        const emailCount = data.split('\n').filter(line => line.trim()).length;
        lists.push({
          name: file.replace('.txt', ''),
          count: emailCount
        });
      }
    }

    return lists;
  } catch (error) {
    console.error('Error loading email lists:', error);
    return [];
  }
});

// Create a new email list
ipcMain.handle('create-email-list', async (event, { name, emails }) => {
  try {
    const filePath = path.join(LISTS_DIR, `${name}.txt`);
    fs.writeFileSync(filePath, emails.join('\n'));
    return true;
  } catch (error) {
    console.error('Error creating email list:', error);
    return false;
  }
});

// Delete an email list
ipcMain.handle('delete-email-list', async (event, name) => {
  try {
    const filePath = path.join(LISTS_DIR, `${name}.txt`);
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
    return true;
  } catch (error) {
    console.error('Error deleting email list:', error);
    return false;
  }
});

// Get contents of an email list
ipcMain.handle('get-email-list-content', async (event, name) => {
  try {
    const filePath = path.join(LISTS_DIR, `${name}.txt`);
    if (fs.existsSync(filePath)) {
      const data = fs.readFileSync(filePath, 'utf8');
      return data.split('\n').filter(email => email.trim());
    }
    return [];
  } catch (error) {
    console.error('Error getting email list content:', error);
    return [];
  }
});

// ============================================================
// History Management
// ============================================================

// Save campaign settings to history
ipcMain.handle('save-history', async (event, historyData) => {
  try {
    const timestamp = new Date().toISOString().replace(/:/g, '-');
    const fileName = `history_${timestamp}.json`;
    const filePath = path.join(HISTORY_DIR, fileName);

    fs.writeFileSync(filePath, JSON.stringify(historyData, null, 2));
    return { success: true, filePath };
  } catch (error) {
    console.error('Error saving history:', error);
    return { success: false, error: error.message };
  }
});

// Load campaign settings from history
ipcMain.handle('load-history', async () => {
  try {
    const { filePaths } = await dialog.showOpenDialog({
      properties: ['openFile'],
      defaultPath: HISTORY_DIR,
      filters: [{ name: 'JSON', extensions: ['json'] }]
    });

    if (filePaths && filePaths.length > 0) {
      const data = fs.readFileSync(filePaths[0], 'utf8');
      return { success: true, data: JSON.parse(data) };
    }
    return { success: false, error: 'No file selected' };
  } catch (error) {
    console.error('Error loading history:', error);
    return { success: false, error: error.message };
  }
});

// ============================================================
// Client File Management
// ============================================================

// Get all client files
ipcMain.handle('get-client-files', async () => {
  try {
    if (!fs.existsSync(CLIENTS_DIR)) {
      fs.mkdirSync(CLIENTS_DIR, { recursive: true });
      return [];
    }

    const files = fs.readdirSync(CLIENTS_DIR);
    return files.filter(file => file.endsWith('.json'));
  } catch (error) {
    console.error('Error getting client files:', error);
    return [];
  }
});

// Upload a client file
ipcMain.handle('upload-client-file', async () => {
  try {
    const { filePaths } = await dialog.showOpenDialog({
      properties: ['openFile'],
      filters: [{ name: 'JSON', extensions: ['json'] }]
    });

    if (filePaths && filePaths.length > 0) {
      const fileName = path.basename(filePaths[0]);
      const destPath = path.join(CLIENTS_DIR, fileName);

      // Copy the file to our clients directory
      fs.copyFileSync(filePaths[0], destPath);
      return { success: true, fileName };
    }
    return { success: false, error: 'No file selected' };
  } catch (error) {
    console.error('Error uploading client file:', error);
    return { success: false, error: error.message };
  }
});

// Delete a client file
ipcMain.handle('delete-client-file', async (event, filename) => {
  try {
    const filePath = path.join(CLIENTS_DIR, filename);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return { success: false, error: `File ${filename} not found` };
    }

    // Delete the file
    fs.unlinkSync(filePath);
    return { success: true, fileName: filename };
  } catch (error) {
    console.error('Error deleting client file:', error);
    return { success: false, error: error.message };
  }
});

// Get client file associated with an account
ipcMain.handle('get-account-client-file', async (event, account) => {
  try {
    const tokenPath = path.join(TOKENS_DIR, `token_${account}.json`);

    if (fs.existsSync(tokenPath)) {
      const tokenData = JSON.parse(fs.readFileSync(tokenPath, 'utf8'));

      if (tokenData.client_file) {
        // Check if the client file still exists
        const clientPath = path.join(CLIENTS_DIR, tokenData.client_file);
        if (fs.existsSync(clientPath)) {
          return {
            success: true,
            clientFile: tokenData.client_file
          };
        } else {
          return {
            success: false,
            error: `Client file ${tokenData.client_file} no longer exists`
          };
        }
      }
    }

    return { success: false, error: 'No client file associated with this account' };
  } catch (error) {
    console.error('Error getting account client file:', error);
    return { success: false, error: error.message };
  }
});

// ============================================================
// Gmail Authentication
// ============================================================

// Authenticate with Gmail
async function authenticateGmail(account, clientFile = null) {
  try {
    // Check if token exists
    const tokenPath = path.join(TOKENS_DIR, `token_${account}.json`);
    let oauth2Client = null;
    let clientCredentials = null;
    let actualClientFile = clientFile;

    // Check if we have a valid token with client file information
    if (fs.existsSync(tokenPath)) {
      const tokenData = JSON.parse(fs.readFileSync(tokenPath, 'utf8'));

      // If token has client_file information and no specific client file was provided, use the stored one
      if (tokenData.client_file && !clientFile) {
        actualClientFile = tokenData.client_file;
        console.log(`Using stored client file ${actualClientFile} for account ${account}`);
      }
    }

    // If we still don't have a client file, we can't proceed
    if (!actualClientFile) {
      throw new Error('No client credential file specified or found in token');
    }

    // Load client secrets
    const clientPath = path.join(CLIENTS_DIR, actualClientFile);
    if (!fs.existsSync(clientPath)) {
      throw new Error(`Client credential file ${actualClientFile} not found`);
    }

    clientCredentials = JSON.parse(fs.readFileSync(clientPath, 'utf8'));

    // Create OAuth client
    oauth2Client = new OAuth2Client(
      clientCredentials.installed.client_id,
      clientCredentials.installed.client_secret,
      clientCredentials.installed.redirect_uris[0]
    );

    // Check if we have a valid token
    if (fs.existsSync(tokenPath)) {
      const tokenData = JSON.parse(fs.readFileSync(tokenPath, 'utf8'));

      // Set the credentials from the token data
      // We need to extract just the OAuth token properties
      const token = {
        access_token: tokenData.access_token,
        refresh_token: tokenData.refresh_token,
        scope: tokenData.scope,
        token_type: tokenData.token_type,
        id_token: tokenData.id_token,
        expiry_date: tokenData.expiry_date
      };

      oauth2Client.setCredentials(token);

      // Check if token is expired
      if (token.expiry_date && token.expiry_date < Date.now()) {
        // Try to refresh token
        if (token.refresh_token) {
          await oauth2Client.refreshToken(token.refresh_token);
          const newToken = oauth2Client.credentials;

          // Preserve the client_file information
          const updatedTokenData = {
            ...newToken,
            client_file: actualClientFile
          };

          fs.writeFileSync(tokenPath, JSON.stringify(updatedTokenData, null, 2));
        } else {
          // Need new token
          return await getNewToken(oauth2Client, tokenPath, account, actualClientFile);
        }
      }
    } else {
      // No token exists, get new one
      return await getNewToken(oauth2Client, tokenPath, account, actualClientFile);
    }

    return oauth2Client;
  } catch (error) {
    console.error(`Authentication error for ${account}:`, error);
    throw error;
  }
}

// Get a new OAuth token
async function getNewToken(oauth2Client, tokenPath, account, clientFile) {
  return new Promise((resolve, reject) => {
    const authUrl = oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: SCOPES,
      prompt: 'consent'  // Force to get refresh_token
    });

    // Launch auth URL in default browser
    shell.openExternal(authUrl);

    // Create a small window for code input
    const codeWindow = new BrowserWindow({
      width: 500,
      height: 250,
      parent: mainWindow,
      modal: true,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false
      }
    });

    // Create a simple HTML page for code input
    codeWindow.loadURL(`data:text/html,
      <html>
        <head><title>Enter Authorization Code</title></head>
        <body style="font-family: sans-serif; padding: 20px;">
          <h3>Enter the authorization code for ${account}</h3>
          <input id="code" style="width: 100%; padding: 8px; margin-bottom: 10px;" />
          <button id="submit" style="padding: 8px 16px;">Submit</button>
          <script>
            document.getElementById('submit').onclick = () => {
              const code = document.getElementById('code').value;
              window.electronAPI.submitCode(code);
            }
          </script>
        </body>
      </html>
    `);

    // Define API for code submission
    codeWindow.webContents.executeJavaScript(`
      window.electronAPI = {
        submitCode: (code) => {
          window.electronAPI.ipcRenderer.send('submit-auth-code', code);
        },
        ipcRenderer: {
          send: (channel, data) => {
            if (channel === 'submit-auth-code') {
              window.postMessage({ type: 'submit-auth-code', code: data }, '*');
            }
          }
        }
      };
    `);

    // Listen for code submission
    codeWindow.webContents.on('console-message', (event, level, message) => {
      console.log('Console message from auth window:', message);
    });

    codeWindow.webContents.on('did-finish-load', () => {
      codeWindow.webContents.executeJavaScript(`
        window.addEventListener('message', (event) => {
          if (event.data.type === 'submit-auth-code') {
            // Use IPC to send code back to main process
            require('electron').ipcRenderer.send('submit-auth-code', event.data.code);
          }
        });
      `);
    });

    // Handle the submitted code
    ipcMain.once('submit-auth-code', async (e, code) => {
      try {
        const { tokens } = await oauth2Client.getToken(code);
        oauth2Client.setCredentials(tokens);

        // Add client file information to the token data
        const tokenData = {
          ...tokens,
          client_file: clientFile  // Store which client file was used for this token
        };

        // Save token to file with client file information
        fs.writeFileSync(tokenPath, JSON.stringify(tokenData, null, 2));

        codeWindow.close();
        resolve(oauth2Client);

        // No longer automatically log out - this was causing all accounts to be logged out
        // Instead, we'll allow users to stay logged in between accounts
      } catch (error) {
        codeWindow.close();
        reject(error);
      }
    });

    codeWindow.on('closed', () => {
      // Handle case when user closes window without submitting code
      ipcMain.removeAllListeners('submit-auth-code');
      reject(new Error('Authentication window was closed'));
    });
  });
}

// ============================================================
// Email Sending
// ============================================================

// Send a single email
async function sendEmail(auth, from, to, subject, body, headers, options = {}) {
  try {
    // Replace tags in the content
    subject = replaceTags(subject, from);
    body = replaceTags(body, from);

    // Apply tracking if enabled
    if (options.enableTracking) {
      // Add tracking to the email body
      body = addTrackingToEmail(body, to, options.campaignId);

      // Update campaign data
      if (options.campaignId && !trackingData.campaigns[options.campaignId]) {
        trackingData.campaigns[options.campaignId] = {
          total: 0,
          opens: 0,
          clicks: 0
        };
      }

      if (options.campaignId) {
        trackingData.campaigns[options.campaignId].total++;
      }
    }

    // Create Gmail API client
    const gmail = google.gmail({ version: 'v1', auth });

    // Build the email
    const emailLines = [];

    // From line with name and email
    emailLines.push(`From: ${from}`);

    // To line
    emailLines.push(`To: ${to}`);

    // Subject line
    emailLines.push(`Subject: ${subject}`);

    // Custom headers
    if (headers && typeof headers === 'object') {
      for (const [key, value] of Object.entries(headers)) {
        emailLines.push(`${key}: ${replaceTags(value, from)}`);
      }
    }

    // MIME-Type header for HTML content
    emailLines.push('MIME-Version: 1.0');
    emailLines.push('Content-Type: text/html; charset=utf-8');

    // Empty line to separate headers from body
    emailLines.push('');

    // Add the HTML content
    emailLines.push(body);

    // Join all lines and encode as base64
    const email = emailLines.join('\r\n');
    const encodedEmail = Buffer.from(email).toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=+$/, '');

    // Send the email
    const res = await gmail.users.messages.send({
      userId: 'me',
      requestBody: {
        raw: encodedEmail
      }
    });

    return { success: true, messageId: res.data.id };
  } catch (error) {
    console.error('Error sending email:', error);
    return { success: false, error: error.message };
  }
}

// Send test emails
ipcMain.handle('send-test-email', async (event, {
  accounts,
  subject,
  body,
  headers,
  testEmails,
  clientFile,
  enableTracking,
  trackingCampaignId
}) => {
  const results = [];

  // Generate a campaign ID if tracking is enabled but no ID provided
  const campaignId = enableTracking && !trackingCampaignId
    ? 'test_' + Math.random().toString(36).substring(2, 15)
    : trackingCampaignId;

  // Get all available client files
  let availableClientFiles = [];
  try {
    if (fs.existsSync(CLIENTS_DIR)) {
      availableClientFiles = fs.readdirSync(CLIENTS_DIR).filter(file => file.endsWith('.json'));
    }
  } catch (error) {
    console.error('Error reading client files directory:', error);
  }

  // If no client file is provided and we have only one available, use it as default
  let defaultClientFile = clientFile;
  if (!defaultClientFile && availableClientFiles.length === 1) {
    defaultClientFile = availableClientFiles[0];
    console.log(`Using the only available client file: ${defaultClientFile}`);
  }

  for (const account of accounts) {
    try {
      // Try multiple authentication methods in order of preference
      let auth = null;
      let authMethod = '';

      // 1. Try to authenticate with stored client file for this account
      try {
        auth = await authenticateGmail(account);
        authMethod = 'stored credentials';
      } catch (storedAuthError) {
        console.log(`No stored credentials for ${account}: ${storedAuthError.message}`);

        // 2. Try with the provided client file
        if (defaultClientFile) {
          try {
            auth = await authenticateGmail(account, defaultClientFile);
            authMethod = `provided client file (${defaultClientFile})`;
          } catch (providedAuthError) {
            console.log(`Failed to authenticate ${account} with provided client file: ${providedAuthError.message}`);

            // 3. Try each available client file until one works
            if (availableClientFiles.length > 0) {
              for (const file of availableClientFiles) {
                if (file === defaultClientFile) continue; // Skip the one we already tried

                try {
                  auth = await authenticateGmail(account, file);
                  authMethod = `auto-selected client file (${file})`;
                  break;
                } catch (autoAuthError) {
                  console.log(`Failed to authenticate ${account} with ${file}: ${autoAuthError.message}`);
                }
              }
            }
          }
        }
      }

      // If we successfully authenticated, send the test emails
      if (auth) {
        console.log(`Successfully authenticated ${account} using ${authMethod}`);

        for (const testEmail of testEmails) {
          // Send the email with tracking options if enabled
          const result = await sendEmail(auth, account, testEmail, subject, body, headers, {
            enableTracking,
            campaignId
          });

          results.push({
            account,
            recipient: testEmail,
            success: result.success,
            error: result.error,
            messageId: result.messageId,
            tracked: enableTracking,
            authMethod: authMethod
          });
        }
      } else {
        // All authentication methods failed
        throw new Error('Failed to authenticate with any available client credentials');
      }
    } catch (error) {
      for (const testEmail of testEmails) {
        results.push({
          account,
          recipient: testEmail,
          success: false,
          error: `Authentication Error: ${error.message}`
        });
      }
    }
  }

  return results;
});

// Start the email campaign
async function startSendingProcess(
  accounts,
  subject,
  body,
  headers,
  selectedLists,
  emailRange,
  timeInterval,
  emailsPerAccount = 10,
  batchSize = 1,
  testEmailInterval = 0,
  testEmails = "",
  clientFile = null,
  enableTracking = false,
  trackingCampaignId = ""
) {
  try {
    // Initialize counters
    let totalEmailCount = 0;
    let sentCount = 0;
    let errorCount = 0;
    let currentAccount = 0;

    // Load all email lists
    let allEmails = [];
    for (const listName of selectedLists) {
      const listFile = path.join(LISTS_DIR, `${listName}.txt`);
      if (fs.existsSync(listFile)) {
        const listEmails = fs.readFileSync(listFile, 'utf8')
          .split('\n')
          .filter(email => email.trim());
        allEmails = [...allEmails, ...listEmails];
      }
    }

    // Remove duplicates
    allEmails = [...new Set(allEmails)];

    // Apply email range if specified
    if (emailRange && emailRange.start && emailRange.end) {
      const start = emailRange.start - 1; // Convert to 0-based index
      const end = Math.min(emailRange.end, allEmails.length);
      allEmails = allEmails.slice(start, end);
    }

    totalEmailCount = allEmails.length;

    // No emails to send
    if (totalEmailCount === 0) {
      mainWindow.webContents.send('send-progress', {
        status: 'error',
        message: 'No emails to send. Please check your selected lists and email range.',
        progress: 0,
        sent: 0,
        total: 0,
        errors: 0
      });
      mainWindow.webContents.send('sending-stopped');
      return;
    }

    // Initialize progress
    mainWindow.webContents.send('send-progress', {
      status: 'starting',
      message: `Starting campaign with ${totalEmailCount} emails`,
      progress: 0,
      sent: 0,
      total: totalEmailCount,
      errors: 0
    });

    // Pre-authenticate all accounts
    const authClients = [];

    // Get all available client files
    let availableClientFiles = [];
    try {
      if (fs.existsSync(CLIENTS_DIR)) {
        availableClientFiles = fs.readdirSync(CLIENTS_DIR).filter(file => file.endsWith('.json'));
      }
    } catch (error) {
      console.error('Error reading client files directory:', error);
    }

    // If no client file is provided and we have only one available, use it as default
    let defaultClientFile = clientFile;
    if (!defaultClientFile && availableClientFiles.length === 1) {
      defaultClientFile = availableClientFiles[0];
      console.log(`Using the only available client file: ${defaultClientFile}`);

      mainWindow.webContents.send('send-progress', {
        status: 'info',
        message: `Using the only available client file: ${defaultClientFile}`,
        details: `Using client file: ${defaultClientFile}`,
        isSuccess: true,
        progress: 0,
        sent: 0,
        total: totalEmailCount,
        errors: errorCount
      });
    }

    for (const account of accounts) {
      try {
        // Try multiple authentication methods in order of preference
        let auth = null;
        let authMethod = '';

        // 1. Try to authenticate with stored client file for this account
        try {
          auth = await authenticateGmail(account);
          authMethod = 'stored credentials';
        } catch (storedAuthError) {
          console.log(`No stored credentials for ${account}: ${storedAuthError.message}`);

          // 2. Try with the provided client file
          if (defaultClientFile) {
            try {
              auth = await authenticateGmail(account, defaultClientFile);
              authMethod = `provided client file (${defaultClientFile})`;
            } catch (providedAuthError) {
              console.log(`Failed to authenticate ${account} with provided client file: ${providedAuthError.message}`);

              // 3. Try each available client file until one works
              if (availableClientFiles.length > 0) {
                for (const file of availableClientFiles) {
                  if (file === defaultClientFile) continue; // Skip the one we already tried

                  try {
                    auth = await authenticateGmail(account, file);
                    authMethod = `auto-selected client file (${file})`;
                    break;
                  } catch (autoAuthError) {
                    console.log(`Failed to authenticate ${account} with ${file}: ${autoAuthError.message}`);
                  }
                }
              }
            }
          }
        }

        // If we successfully authenticated, add to the list
        if (auth) {
          authClients.push({
            account,
            auth,
            error: null
          });

          mainWindow.webContents.send('send-progress', {
            status: 'info',
            message: `Successfully authenticated ${account}`,
            details: `${account}: Using ${authMethod}`,
            isSuccess: true,
            progress: 0,
            sent: 0,
            total: totalEmailCount,
            errors: errorCount
          });
        } else {
          // All authentication methods failed
          throw new Error('Failed to authenticate with any available client credentials');
        }
      } catch (error) {
        authClients.push({
          account,
          auth: null,
          error: error.message
        });

        mainWindow.webContents.send('send-progress', {
          status: 'auth-error',
          message: `Authentication failed for ${account}: ${error.message}`,
          details: `${account}: ${error.message}`,
          progress: 0,
          sent: 0,
          total: totalEmailCount,
          errors: errorCount + 1
        });

        errorCount++;
      }
    }

    // If no accounts could be authenticated, stop the process
    if (authClients.every(client => client.auth === null)) {
      mainWindow.webContents.send('send-progress', {
        status: 'error',
        message: 'Failed to authenticate any accounts. Campaign cannot start.',
        progress: 0,
        sent: 0,
        total: totalEmailCount,
        errors: errorCount
      });
      mainWindow.webContents.send('sending-stopped');
      return;
    }

    // Track emails sent per account
    const accountEmailCount = {};
    authClients.forEach(client => {
      accountEmailCount[client.account] = 0;
    });

    // Send emails with rotation and batch processing
    for (let i = 0; i < allEmails.length; i += batchSize) {
      // Check if we need to stop or pause
      if (stopSending) {
        mainWindow.webContents.send('send-progress', {
          status: 'stopped',
          message: `Campaign stopped. Sent ${sentCount} of ${totalEmailCount} emails with ${errorCount} errors.`,
          progress: (sentCount / totalEmailCount) * 100,
          sent: sentCount,
          total: totalEmailCount,
          errors: errorCount
        });
        mainWindow.webContents.send('sending-stopped');
        return;
      }

      // Handle pausing
      while (pauseSending && !stopSending) {
        mainWindow.webContents.send('send-progress', {
          status: 'paused',
          message: `Campaign paused. Sent ${sentCount} of ${totalEmailCount} emails so far.`,
          progress: (sentCount / totalEmailCount) * 100,
          sent: sentCount,
          total: totalEmailCount,
          errors: errorCount
        });

        // Wait a bit before checking again
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // If we were paused and then resumed, send a resumed notification
      if (!pauseSending) {
        mainWindow.webContents.send('send-progress', {
          status: 'resumed',
          message: `Campaign resumed. Continuing with email ${sentCount + 1} of ${totalEmailCount}.`,
          progress: (sentCount / totalEmailCount) * 100,
          sent: sentCount,
          total: totalEmailCount,
          errors: errorCount
        });
      }

      // Get the next account to use based on rotation settings
      const clientIndex = currentAccount % authClients.length;
      const client = authClients[clientIndex];

      // Skip accounts that failed authentication
      if (client.auth === null) {
        currentAccount++;
        continue;
      }

      // Check if we need to rotate to next account (reached emails per account limit)
      if (accountEmailCount[client.account] >= emailsPerAccount) {
        // Reset count for this account and move to next one
        accountEmailCount[client.account] = 0;
        currentAccount++;
        i -= batchSize; // Retry this batch with the next account
        continue;
      }

      // Process a batch of emails with the current account
      const batch = allEmails.slice(i, i + batchSize);
      let batchSuccess = 0;

      for (const to of batch) {
        try {
          // Send the email with tracking options if enabled
          const result = await sendEmail(client.auth, client.account, to, subject, body, headers, {
            enableTracking: enableTracking,
            campaignId: trackingCampaignId
          });

          if (result.success) {
            sentCount++;
            batchSuccess++;

            mainWindow.webContents.send('send-progress', {
              status: 'sending',
              message: `Sent ${sentCount} of ${totalEmailCount} emails`,
              details: `${client.account} → ${to}: Success`,
              isSuccess: true,
              progress: (sentCount / totalEmailCount) * 100,
              sent: sentCount,
              total: totalEmailCount,
              errors: errorCount
            });

            // Check if we need to send a test email based on the interval
            if (testEmailInterval > 0 && testEmails && sentCount % testEmailInterval === 0) {
              const testEmailAddresses = testEmails.split(/[,;\n]/).map(e => e.trim()).filter(e => e.length > 0);

              if (testEmailAddresses.length > 0) {
                mainWindow.webContents.send('send-progress', {
                  status: 'info',
                  message: `Sending test email at ${sentCount} emails sent mark`,
                  isSuccess: true,
                  progress: (sentCount / totalEmailCount) * 100,
                  sent: sentCount,
                  total: totalEmailCount,
                  errors: errorCount
                });

                // Send test emails with the current authenticated client
                for (const testEmailAddr of testEmailAddresses) {
                  try {
                    const testResult = await sendEmail(client.auth, client.account, testEmailAddr,
                      `[TEST EMAIL] ${subject} - After ${sentCount} emails`, body, headers, {
                        enableTracking: enableTracking,
                        campaignId: trackingCampaignId ? `${trackingCampaignId}_test` : ''
                      });

                    mainWindow.webContents.send('send-progress', {
                      status: testResult.success ? 'info' : 'error',
                      message: `Test email at ${sentCount} mark`,
                      details: `${client.account} → ${testEmailAddr}: ${testResult.success ? 'Success' : testResult.error}`,
                      isSuccess: testResult.success,
                      progress: (sentCount / totalEmailCount) * 100,
                      sent: sentCount,
                      total: totalEmailCount,
                      errors: errorCount
                    });
                  } catch (testError) {
                    mainWindow.webContents.send('send-progress', {
                      status: 'error',
                      message: `Error sending test email to ${testEmailAddr}`,
                      details: `${client.account} → ${testEmailAddr}: ${testError.message}`,
                      isSuccess: false,
                      progress: (sentCount / totalEmailCount) * 100,
                      sent: sentCount,
                      total: totalEmailCount,
                      errors: errorCount
                    });
                  }
                }
              }
            }
          } else {
            errorCount++;

            mainWindow.webContents.send('send-progress', {
              status: 'error',
              message: `Error sending email to ${to}`,
              details: `${client.account} → ${to}: ${result.error}`,
              isSuccess: false,
              progress: (sentCount / totalEmailCount) * 100,
              sent: sentCount,
              total: totalEmailCount,
              errors: errorCount
            });
          }
        } catch (error) {
          errorCount++;

          mainWindow.webContents.send('send-progress', {
            status: 'error',
            message: `Error sending email to ${to}`,
            details: `${client.account} → ${to}: ${error.message}`,
            isSuccess: false,
            progress: (sentCount / totalEmailCount) * 100,
            sent: sentCount,
            total: totalEmailCount,
            errors: errorCount
          });
        }
      }

      // Update account email count
      accountEmailCount[client.account] += batchSuccess;

      // Log rotation status
      mainWindow.webContents.send('send-progress', {
        status: 'info',
        message: `Account ${client.account} has sent ${accountEmailCount[client.account]}/${emailsPerAccount} emails in rotation`,
        isSuccess: true,
        progress: (sentCount / totalEmailCount) * 100,
        sent: sentCount,
        total: totalEmailCount,
        errors: errorCount
      });

      // Check if we need to rotate for the next batch
      if (accountEmailCount[client.account] >= emailsPerAccount) {
        currentAccount++;
      }

      // Wait for the specified interval before sending the next batch
      // Only wait if we're not at the end of the list
      if (i + batchSize < allEmails.length && !pauseSending && !stopSending) {
        await new Promise(resolve => setTimeout(resolve, timeInterval * 1000));
      }
    }

    // Campaign completed
    mainWindow.webContents.send('send-progress', {
      status: 'completed',
      message: `Campaign completed. Sent ${sentCount} of ${totalEmailCount} emails with ${errorCount} errors.`,
      progress: 100,
      sent: sentCount,
      total: totalEmailCount,
      errors: errorCount
    });

    mainWindow.webContents.send('sending-stopped');
  } catch (error) {
    console.error('Error in sending process:', error);

    mainWindow.webContents.send('send-progress', {
      status: 'error',
      message: `Campaign failed: ${error.message}`,
      progress: 0,
      sent: 0,
      total: 0,
      errors: 1
    });

    mainWindow.webContents.send('sending-stopped');
  }
}

// Start the email campaign
ipcMain.handle('start-email-campaign', async (event, {
  accounts,
  subject,
  body,
  headers,
  selectedLists,
  emailRange,
  timeInterval,
  emailsPerAccount,
  batchSize,
  testEmailInterval,
  testEmails,
  clientFile
}) => {
  // Reset control flags
  pauseSending = false;
  stopSending = false;

  // Start the sending process in a separate async function
  sendingProcess = startSendingProcess(
    accounts,
    subject,
    body,
    headers,
    selectedLists,
    emailRange,
    timeInterval,
    emailsPerAccount,
    batchSize,
    testEmailInterval,
    testEmails,
    clientFile
  );

  return { success: true };
});

// Pause the email campaign
ipcMain.on('pause-email-campaign', () => {
  pauseSending = true;
  mainWindow.webContents.send('sending-paused');
});

// Resume the email campaign
ipcMain.on('resume-email-campaign', () => {
  pauseSending = false;
  mainWindow.webContents.send('sending-resumed');
});

// Stop the email campaign
ipcMain.on('stop-email-campaign', () => {
  stopSending = true;
  // The sending process will notice this flag and stop gracefully
});

// ============================================================
// Email Tracking Server Endpoints
// ============================================================

// Create a simple Express server for tracking
// trackingApp and PORT are already declared at the top of the file

// Parse URL-encoded bodies
trackingApp.use(express.urlencoded({ extended: true }));

// Track email opens
trackingApp.get('/track/open', (req, res) => {
  const { email, campaign } = req.query;

  if (!email) {
    res.status(400).send('Missing required parameters');
    return;
  }

  // Log the open event
  console.log(`Tracking: Email opened by ${email}, campaign: ${campaign || 'unknown'}`);

  // Get device information from user agent
  const userAgent = req.headers['user-agent'] || 'Unknown';
  const device = userAgent.includes('Mobile') ? 'Mobile' : 'Desktop';

  // Get IP address
  const ip = req.headers['x-forwarded-for'] || req.socket.remoteAddress;

  // Track the open in our tracking data
  const trackData = {
    email,
    campaign: campaign || 'unknown',
    time: new Date(),
    device,
    ip
  };

  if (mainWindow) {
    mainWindow.webContents.send('tracking-event', {
      type: 'open',
      data: trackData
    });
  }

  // For tracking pixels, we need to return a 1x1 transparent GIF
  const transparentGif = Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64');
  res.writeHead(200, {
    'Content-Type': 'image/gif',
    'Content-Length': transparentGif.length,
    'Cache-Control': 'no-store, no-cache, must-revalidate, private'
  });
  res.end(transparentGif);
});

// Track link clicks
trackingApp.get('/track/click', (req, res) => {
  const { email, campaign, url } = req.query;

  if (!email || !url) {
    res.status(400).send('Missing required parameters');
    return;
  }

  // Log the click event
  console.log(`Tracking: Link clicked by ${email}, campaign: ${campaign || 'unknown'}, URL: ${url}`);

  // Get device information from user agent
  const userAgent = req.headers['user-agent'] || 'Unknown';
  const device = userAgent.includes('Mobile') ? 'Mobile' : 'Desktop';

  // Track the click in our tracking data
  const trackData = {
    email,
    campaign: campaign || 'unknown',
    link: decodeURIComponent(url),
    time: new Date(),
    device
  };

  if (mainWindow) {
    mainWindow.webContents.send('tracking-event', {
      type: 'click',
      data: trackData
    });
  }

  // Redirect to the original URL
  res.redirect(url);
});

// Create HTTP server
// trackingServer is already declared at the top of the file

// Start tracking server
function startTrackingServer() {
  if (trackingServer) {
    return;
  }

  trackingServer = http.createServer(trackingApp);

  trackingServer.listen(PORT, '0.0.0.0', () => {
    console.log(`Tracking server running on port ${PORT}`);
  });

  trackingServer.on('error', (error) => {
    console.error(`Error starting tracking server: ${error.message}`);
    trackingServer = null;
  });
}

// Stop tracking server
function stopTrackingServer() {
  if (trackingServer) {
    trackingServer.close();
    trackingServer = null;
    console.log('Tracking server stopped');
  }
}

// Get tracking data for dashboard
ipcMain.handle('get-tracking-data', async () => {
  return trackingData;
});

// Listen for tracking events from the main process
ipcMain.handle('track-email-open', async (event, data) => {
  try {
    // Update tracking data
    trackingData.opens.total++;

    // Update unique opens if this is the first open for this email
    if (!trackingData.opens.byEmail[data.email]) {
      trackingData.opens.byEmail[data.email] = { count: 0, lastOpened: new Date() };
      trackingData.opens.unique++;
    }

    // Update this email's open count
    trackingData.opens.byEmail[data.email].count++;
    trackingData.opens.byEmail[data.email].lastOpened = new Date();

    // Add to recent activity
    trackingData.opens.recentActivity.unshift({
      email: data.email,
      time: new Date(),
      device: data.device || 'Unknown',
      ip: data.ip || 'Unknown'
    });

    // Limit recent activity size
    if (trackingData.opens.recentActivity.length > 100) {
      trackingData.opens.recentActivity.pop();
    }

    // Save tracking data
    saveTrackingData();

    return { success: true };
  } catch (error) {
    console.error('Error tracking email open:', error);
    return { success: false, error: error.message };
  }
});

// Listen for link click events from the main process
ipcMain.handle('track-link-click', async (event, data) => {
  try {
    // Update tracking data
    trackingData.clicks.total++;

    // Update unique clicks if this is the first click for this email
    if (!trackingData.clicks.byEmail[data.email]) {
      trackingData.clicks.byEmail[data.email] = {
        count: 0,
        lastClicked: new Date(),
        links: {}
      };
      trackingData.clicks.unique++;
    }

    // Update this email's click count
    trackingData.clicks.byEmail[data.email].count++;
    trackingData.clicks.byEmail[data.email].lastClicked = new Date();

    // Update this email's links
    if (!trackingData.clicks.byEmail[data.email].links[data.link]) {
      trackingData.clicks.byEmail[data.email].links[data.link] = 0;
    }
    trackingData.clicks.byEmail[data.email].links[data.link]++;

    // Update link stats
    if (!trackingData.clicks.byLink[data.link]) {
      trackingData.clicks.byLink[data.link] = {
        count: 0,
        uniqueCount: 0,
        lastClicked: new Date(),
        emails: {}
      };
    }

    // Update this link's click count
    trackingData.clicks.byLink[data.link].count++;
    trackingData.clicks.byLink[data.link].lastClicked = new Date();

    // Update unique clicks for this link
    if (!trackingData.clicks.byLink[data.link].emails[data.email]) {
      trackingData.clicks.byLink[data.link].emails[data.email] = true;
      trackingData.clicks.byLink[data.link].uniqueCount++;
    }

    // Add to recent activity
    trackingData.clicks.recentActivity.unshift({
      email: data.email,
      link: data.link,
      time: new Date(),
      device: data.device || 'Unknown'
    });

    // Limit recent activity size
    if (trackingData.clicks.recentActivity.length > 100) {
      trackingData.clicks.recentActivity.pop();
    }

    // Save tracking data
    saveTrackingData();

    return { success: true };
  } catch (error) {
    console.error('Error tracking link click:', error);
    return { success: false, error: error.message };
  }
});

// Start the tracking server when the app is ready
app.whenReady().then(() => {
  // Start tracking server
  startTrackingServer();

  // Load tracking data
  loadTrackingData();
});

// This is already defined above, so removing the duplicate

// Stop the tracking server when the app is closed
app.on('will-quit', () => {
  stopTrackingServer();
});

// ============================================================
// IPC Events for Process Control
// ============================================================

// Main function that runs the application
// This is the main entry point for the application
// To run: electron .