/**
 * <PERSON><PERSON><PERSON>ript for Email Marketing Application
 * Handles UI interactions and updates
 */

// State variables
let selectedAccounts = [];
let selectedLists = [];
let currentTab = 'email-composition';
let sendingInProgress = false;
let sendingPaused = true;

// UI tabs
const TABS = {
  EMAIL_COMPOSITION: 'email-composition',
  ACCOUNTS: 'accounts',
  LISTS: 'lists',
  HISTORY: 'history',
  DASHBOARD: 'dashboard'
};

// Dashboard data structures
const dashboardData = {
  totalEmails: 0,
  successCount: 0,
  errorCount: 0,
  accountPerformance: {}, // key: account, value: {sent: 0, success: 0, errors: 0}
  timePoints: [], // Array of timestamps for the sending rate chart
  sentCountPoints: [], // Array of cumulative sent counts for the sending rate chart
  successPoints: [], // Array of cumulative success counts for the success/error chart
  errorPoints: [], // Array of cumulative error counts for the success/error chart
};

// Tracking data structures
const trackingData = {
  // Campaign information
  campaignId: '',
  trackerUrl: '',
  startTime: null,
  totalRecipients: 0,

  // Open tracking data
  opens: {
    total: 0,
    unique: 0,
    byEmail: {}, // key: email, value: {count: number, lastOpened: Date}
    timePoints: [], // Array of timestamps
    countPoints: [], // Array of cumulative open counts
    recentActivity: [] // Array of {email, time, device, ip}
  },

  // Click tracking data
  clicks: {
    total: 0,
    unique: 0,
    byEmail: {}, // key: email, value: {count: number, lastClicked: Date, links: {}}
    byLink: {}, // key: link, value: {count: number, uniqueCount: number, lastClicked: Date}
    timePoints: [], // Array of timestamps
    countPoints: [], // Array of cumulative click counts
    recentActivity: [] // Array of {email, link, time, device}
  }
};

// DOM ready event
document.addEventListener('DOMContentLoaded', () => {
  // Initialize the UI
  initializeUI();

  // Animate the MH-SENDER title with staggered animation
  animateBrandTitle();

  // Load accounts
  loadAccounts();

  // Load email lists
  loadEmailLists();

  // Load client files
  loadClientFiles();

  // Attach event listeners
  attachEventListeners();
});

/**
 * Animates the brand title with a nice effect
 */
function animateBrandTitle() {
  const title = document.getElementById('brand-title');
  if (!title) return;

  // Add the pulse animation class after a slight delay
  setTimeout(() => {
    title.classList.add('animate__animated', 'animate__pulse');

    // Remove animation classes after animation completes to allow for repeating
    title.addEventListener('animationend', () => {
      title.classList.remove('animate__animated', 'animate__pulse');
    });
  }, 1500);
}

/**
 * Initialize the UI elements
 */
function initializeUI() {
  // Initialize tabs
  document.querySelectorAll('.nav-link').forEach(tabButton => {
    tabButton.addEventListener('click', (e) => {
      e.preventDefault();
      const targetTab = e.target.closest('.nav-link').getAttribute('data-tab');
      switchTab(targetTab);
    });
  });

  // Initialize dashboard
  initializeDashboard();

  // Start with email composition tab
  switchTab(TABS.EMAIL_COMPOSITION);
}

/**
 * Switch between UI tabs
 * @param {string} tabId - The ID of the tab to switch to
 */
function switchTab(tabId) {
  // Hide all tabs
  document.querySelectorAll('.tab-content').forEach(tab => {
    tab.style.display = 'none';
  });

  // Deactivate all tab buttons
  document.querySelectorAll('.nav-link').forEach(button => {
    button.classList.remove('active');
  });

  // Show the selected tab
  document.getElementById(tabId).style.display = 'block';

  // Activate the tab button
  document.querySelector(`.nav-link[data-tab="${tabId}"]`).classList.add('active');

  // Update current tab
  currentTab = tabId;
}

/**
 * Attach event listeners to UI elements
 */
function attachEventListeners() {
  // Account management
  document.getElementById('add-account-btn').addEventListener('click', addAccount);
  document.getElementById('delete-account-btn').addEventListener('click', deleteSelectedAccounts);

  // Email list management
  document.getElementById('create-list-btn').addEventListener('click', showCreateListModal);
  document.getElementById('save-list-btn').addEventListener('click', saveEmailList);
  document.getElementById('delete-list-btn').addEventListener('click', deleteSelectedLists);

  // Client file management
  document.getElementById('upload-client-btn').addEventListener('click', uploadClientFile);
  document.getElementById('delete-client-btn').addEventListener('click', deleteClientFile);

  // HTML preview
  document.getElementById('preview-html-btn').addEventListener('click', previewHtml);
  document.getElementById('close-preview-modal').addEventListener('click', closeHtmlPreview);
  document.getElementById('close-preview-btn').addEventListener('click', closeHtmlPreview);

  // Email sending
  document.getElementById('send-test-btn').addEventListener('click', sendTestEmails);
  document.getElementById('start-campaign-btn').addEventListener('click', startEmailCampaign);
  document.getElementById('pause-campaign-btn').addEventListener('click', pauseEmailCampaign);
  document.getElementById('resume-campaign-btn').addEventListener('click', resumeEmailCampaign);
  document.getElementById('stop-campaign-btn').addEventListener('click', stopEmailCampaign);

  // History operations
  document.getElementById('save-history-btn').addEventListener('click', saveCurrentAsHistory);
  document.getElementById('load-history-btn').addEventListener('click', loadHistory);

  // Account selection
  document.getElementById('account-listbox').addEventListener('change', () => {
    updateSelectedAccounts();
  });

  // Manage account selection
  const manageAccountListbox = document.getElementById('manage-account-listbox');
  if (manageAccountListbox) {
    manageAccountListbox.addEventListener('change', () => {
      updateSelectedAccounts();
    });
  }

  // List selection in main tab
  document.getElementById('list-listbox').addEventListener('change', () => {
    updateSelectedLists();
  });

  // List selection in manage tab
  const manageListListbox = document.getElementById('manage-list-listbox');
  if (manageListListbox) {
    manageListListbox.addEventListener('change', () => {
      updateSelectedLists();
    });
  }

  // Event listeners for sending progress
  window.electron.onSendProgress((event, data) => {
    updateSendingProgress(data);
  });

  window.electron.onSendingPaused(() => {
    sendingPaused = true;
    updateSendingControls();
  });

  window.electron.onSendingResumed(() => {
    sendingPaused = false;
    updateSendingControls();
  });

  window.electron.onSendingStopped(() => {
    sendingInProgress = false;
    sendingPaused = true;
    updateSendingControls();
  });

  // Event listener for tracking events
  window.electron.onTrackingEvent((event, data) => {
    if (data.type === 'open') {
      trackEmailOpen(data.data);
    } else if (data.type === 'click') {
      trackLinkClick(data.data);
    }
  });

  // Close button for modal
  const closeButtons = document.querySelectorAll('.btn-close');
  closeButtons.forEach(button => {
    button.addEventListener('click', (e) => {
      const modal = e.target.closest('.modal');
      if (modal) {
        modal.style.display = 'none';
      }
    });
  });

  // Cancel buttons for modals
  const cancelButtons = document.querySelectorAll('.modal .btn-secondary');
  cancelButtons.forEach(button => {
    button.addEventListener('click', (e) => {
      const modal = e.target.closest('.modal');
      if (modal) {
        modal.style.display = 'none';
      }
    });
  });
}

// ============================================================
// Account Management Functions
// ============================================================

/**
 * Load accounts from storage
 */
async function loadAccounts() {
  try {
    const accounts = await window.electron.loadAccounts();

    // Get both the main account listbox and the manage account listbox
    const accountListbox = document.getElementById('account-listbox');
    const manageAccountListbox = document.getElementById('manage-account-listbox');

    // Clear both listboxes
    accountListbox.innerHTML = '';
    if (manageAccountListbox) {
      manageAccountListbox.innerHTML = '';
    }

    // Make sure we have accounts to display
    if (accounts && accounts.length > 0) {
      accounts.forEach(account => {
        // Add to the main account listbox
        const option = document.createElement('option');
        option.value = account;
        option.textContent = account;
        accountListbox.appendChild(option);

        // Add to the manage account listbox if it exists
        if (manageAccountListbox) {
          const manageOption = document.createElement('option');
          manageOption.value = account;
          manageOption.textContent = account;
          manageAccountListbox.appendChild(manageOption);
        }
      });

      // Enable the delete button since we have accounts
      document.getElementById('delete-account-btn').disabled = false;
    } else {
      // No accounts, disable delete button
      document.getElementById('delete-account-btn').disabled = true;
    }

    // Clear the selectedAccounts array
    selectedAccounts = [];
  } catch (error) {
    showError('Failed to load accounts', error);
  }
}

/**
 * Update the selected accounts array based on UI selection
 */
function updateSelectedAccounts() {
  // Get the currently active tab
  const currentTabContent = document.querySelector('.tab-content[style*="display: block"]');

  let accountListbox;

  // If we're in the accounts management tab
  if (currentTab === TABS.ACCOUNTS) {
    accountListbox = document.getElementById('manage-account-listbox');
  } else {
    // Otherwise use the regular account listbox from the email composition tab
    accountListbox = document.getElementById('account-listbox');
  }

  if (accountListbox) {
    selectedAccounts = Array.from(accountListbox.selectedOptions).map(option => option.value);
  }
}

/**
 * Add a new account
 */
async function addAccount() {
  const accountInput = document.getElementById('new-account-input');
  const account = accountInput.value.trim();

  if (!account) {
    showError('Please enter an email address');
    return;
  }

  if (!isValidEmail(account)) {
    showError('Please enter a valid email address');
    return;
  }

  try {
    await window.electron.saveAccount(account);
    accountInput.value = '';
    await loadAccounts();
    showSuccess(`Account ${account} added successfully`);
  } catch (error) {
    showError('Failed to add account', error);
  }
}

/**
 * Delete selected accounts
 */
async function deleteSelectedAccounts() {
  if (selectedAccounts.length === 0) {
    showError('Please select at least one account to delete');
    return;
  }

  if (!confirm(`Are you sure you want to delete ${selectedAccounts.length} selected account(s)?`)) {
    return;
  }

  try {
    for (const account of selectedAccounts) {
      await window.electron.deleteAccount(account);
    }

    await loadAccounts();
    selectedAccounts = [];
    showSuccess('Accounts deleted successfully');
  } catch (error) {
    showError('Failed to delete accounts', error);
  }
}

// ============================================================
// HTML Preview Functions
// ============================================================

/**
 * Preview the HTML content in a modal
 */
function previewHtml() {
  const htmlContent = document.getElementById('body-text').value.trim();

  if (!htmlContent) {
    showError('Please enter some HTML content to preview');
    return;
  }

  // Get the preview content container
  const previewContainer = document.getElementById('html-preview-content');

  // Set the HTML content
  previewContainer.innerHTML = htmlContent;

  // Show the modal
  document.getElementById('html-preview-modal').style.display = 'block';
}

/**
 * Close the HTML preview modal
 */
function closeHtmlPreview() {
  document.getElementById('html-preview-modal').style.display = 'none';
}

// ============================================================
// Email List Management Functions
// ============================================================

/**
 * Load email lists from storage
 */
async function loadEmailLists() {
  try {
    const lists = await window.electron.loadEmailLists();

    // Get both the main list listbox and the manage list listbox
    const listListbox = document.getElementById('list-listbox');
    const manageListListbox = document.getElementById('manage-list-listbox');

    // Clear both listboxes
    listListbox.innerHTML = '';
    if (manageListListbox) {
      manageListListbox.innerHTML = '';
    }

    // Make sure we have lists to display
    if (lists && lists.length > 0) {
      lists.forEach(list => {
        // Add to the main list listbox
        const option = document.createElement('option');
        option.value = list.name;
        option.textContent = `${list.name} (${list.count} emails)`;
        listListbox.appendChild(option);

        // Add to the manage list listbox if it exists
        if (manageListListbox) {
          const manageOption = document.createElement('option');
          manageOption.value = list.name;
          manageOption.textContent = `${list.name} (${list.count} emails)`;
          manageListListbox.appendChild(manageOption);
        }
      });

      // Enable the delete button since we have lists
      document.getElementById('delete-list-btn').disabled = false;
    } else {
      // No lists, disable delete button
      document.getElementById('delete-list-btn').disabled = true;
    }

    // Clear the selectedLists array
    selectedLists = [];
  } catch (error) {
    showError('Failed to load email lists', error);
  }
}

/**
 * Update the selected lists array based on UI selection
 */
function updateSelectedLists() {
  // Get the currently active tab
  const currentTabContent = document.querySelector('.tab-content[style*="display: block"]');

  let listListbox;

  // If we're in the lists management tab
  if (currentTab === TABS.LISTS) {
    listListbox = document.getElementById('manage-list-listbox');
  } else {
    // Otherwise use the regular list listbox from the email composition tab
    listListbox = document.getElementById('list-listbox');
  }

  if (listListbox) {
    selectedLists = Array.from(listListbox.selectedOptions).map(option => option.value);
  }
}

/**
 * Show the create list modal
 */
function showCreateListModal() {
  document.getElementById('list-name-input').value = '';
  document.getElementById('list-emails-input').value = '';
  document.getElementById('create-list-modal').style.display = 'block';
}

/**
 * Hide the create list modal
 */
function hideCreateListModal() {
  document.getElementById('create-list-modal').style.display = 'none';
}

/**
 * Save a new email list
 */
async function saveEmailList() {
  const name = document.getElementById('list-name-input').value.trim();
  const emailsText = document.getElementById('list-emails-input').value.trim();

  if (!name) {
    showError('Please enter a list name');
    return;
  }

  if (!emailsText) {
    showError('Please enter at least one email address');
    return;
  }

  // Parse emails (split by comma, semicolon, or newline)
  const emails = emailsText.split(/[,;\n]/).map(email => email.trim()).filter(email => email);

  // Validate emails
  const invalidEmails = emails.filter(email => !isValidEmail(email));
  if (invalidEmails.length > 0) {
    showError(`Found ${invalidEmails.length} invalid email addresses. Please correct them and try again.`);
    return;
  }

  try {
    await window.electron.createEmailList({ name, emails });
    hideCreateListModal();
    await loadEmailLists();
    showSuccess(`Email list "${name}" created with ${emails.length} emails`);
  } catch (error) {
    showError('Failed to create email list', error);
  }
}

/**
 * Delete selected email lists
 */
async function deleteSelectedLists() {
  if (selectedLists.length === 0) {
    showError('Please select at least one list to delete');
    return;
  }

  if (!confirm(`Are you sure you want to delete ${selectedLists.length} selected list(s)?`)) {
    return;
  }

  try {
    for (const list of selectedLists) {
      await window.electron.deleteEmailList(list);
    }

    await loadEmailLists();
    selectedLists = [];
    showSuccess('Email lists deleted successfully');
  } catch (error) {
    showError('Failed to delete email lists', error);
  }
}

// ============================================================
// Client File Management Functions
// ============================================================

/**
 * Load client files from storage
 */
async function loadClientFiles() {
  try {
    const files = await window.electron.getClientFiles();
    const clientSelect = document.getElementById('client-select');
    clientSelect.innerHTML = '';

    // Add an empty option first (for using stored credentials)
    const emptyOption = document.createElement('option');
    emptyOption.value = '';
    emptyOption.textContent = '-- Use stored credentials --';
    clientSelect.appendChild(emptyOption);

    if (files.length === 0) {
      const option = document.createElement('option');
      option.value = '';
      option.textContent = 'No client files available';
      option.disabled = true;
      clientSelect.appendChild(option);
    } else {
      files.forEach(file => {
        const option = document.createElement('option');
        option.value = file;
        option.textContent = file;
        clientSelect.appendChild(option);
      });
    }

    // Add a help text below the select
    const helpText = document.createElement('small');
    helpText.className = 'text-muted d-block mt-1';
    helpText.innerHTML = 'Select a client file only if you need to authenticate new accounts. ' +
                         'For accounts with existing tokens, stored credentials will be used automatically.';

    // Insert the help text after the select
    const parentElement = clientSelect.parentElement;
    const selectContainer = parentElement.querySelector('.d-flex');
    parentElement.insertBefore(helpText, selectContainer.nextSibling);
  } catch (error) {
    showError('Failed to load client files', error);
  }
}

/**
 * Upload a new client file
 */
async function uploadClientFile() {
  try {
    const result = await window.electron.uploadClientFile();

    if (result.success) {
      await loadClientFiles();
      showSuccess(`Client file "${result.fileName}" uploaded successfully`);
    } else {
      showError('Failed to upload client file', result.error);
    }
  } catch (error) {
    showError('Failed to upload client file', error);
  }
}

/**
 * Delete the selected client file
 */
async function deleteClientFile() {
  const clientSelect = document.getElementById('client-select');
  const selectedFile = clientSelect.value;

  if (!selectedFile) {
    showError('Please select a client file to delete');
    return;
  }

  if (!confirm(`Are you sure you want to delete the client file "${selectedFile}"?`)) {
    return;
  }

  try {
    await window.electron.deleteClientFile(selectedFile);
    await loadClientFiles();
    showSuccess(`Client file "${selectedFile}" deleted successfully`);
  } catch (error) {
    showError('Failed to delete client file', error);
  }
}

// ============================================================
// Email Sending Functions
// ============================================================

/**
 * Send test emails
 */
async function sendTestEmails() {
  if (selectedAccounts.length === 0) {
    showError('Please select at least one account');
    return;
  }

  const subject = document.getElementById('subject-input').value.trim();
  if (!subject) {
    showError('Please enter a subject');
    return;
  }

  const body = document.getElementById('body-text').value.trim();
  if (!body) {
    showError('Please enter a message body');
    return;
  }

  const testEmailsText = document.getElementById('test-email-input').value.trim();
  if (!testEmailsText) {
    showError('Please enter at least one test email address');
    return;
  }

  // Parse headers
  let headers = {};
  try {
    const headersText = document.getElementById('headers-text').value.trim();
    if (headersText) {
      headers = JSON.parse(headersText);
    }
  } catch (error) {
    showError('Invalid headers format. Headers must be valid JSON.');
    return;
  }

  // Parse test emails
  const testEmails = testEmailsText.split(/[,;\n]/).map(email => email.trim()).filter(email => email);

  // Validate test emails
  const invalidEmails = testEmails.filter(email => !isValidEmail(email));
  if (invalidEmails.length > 0) {
    showError(`Found ${invalidEmails.length} invalid test email addresses. Please correct them and try again.`);
    return;
  }

  // Get tracking options
  const enableTracking = document.getElementById('enable-tracking').checked;
  const trackingCampaignId = document.getElementById('tracking-campaign-id').value.trim();

  // Show loading state
  const testButton = document.getElementById('send-test-btn');
  const originalText = testButton.textContent;
  testButton.textContent = 'Sending...';
  testButton.disabled = true;

  try {
    const results = await window.electron.sendTestEmail({
      accounts: selectedAccounts,
      subject,
      body,
      headers,
      testEmails,
      enableTracking,
      trackingCampaignId
    });

    // Process results
    let successCount = 0;
    let errorCount = 0;
    const errors = [];
    const authMethods = new Map(); // Track which auth method was used for each account

    results.forEach(result => {
      if (result.success) {
        successCount++;

        // Track which auth method was used for this account
        if (result.authMethod && !authMethods.has(result.account)) {
          authMethods.set(result.account, result.authMethod);
        }
      } else {
        errorCount++;
        errors.push(`${result.account} -> ${result.recipient}: ${result.error}`);
      }
    });

    if (errorCount === 0) {
      // Create a detailed success message including auth methods
      let successMessage = `Successfully sent ${successCount} test email(s)`;

      if (authMethods.size > 0) {
        successMessage += '\n\nAuthentication methods used:';
        authMethods.forEach((method, account) => {
          successMessage += `\n- ${account}: ${method}`;
        });
      }

      showSuccess(successMessage);
    } else {
      showError(`Sent ${successCount} email(s), failed to send ${errorCount} email(s)`, errors.join('\n'));
    }
  } catch (error) {
    showError('Failed to send test emails', error);
  } finally {
    // Restore button state
    testButton.textContent = originalText;
    testButton.disabled = false;
  }
}

/**
 * Start an email campaign
 */
async function startEmailCampaign() {
  if (selectedAccounts.length === 0) {
    showError('Please select at least one account');
    return;
  }

  if (selectedLists.length === 0) {
    showError('Please select at least one email list');
    return;
  }

  const subject = document.getElementById('subject-input').value.trim();
  if (!subject) {
    showError('Please enter a subject');
    return;
  }

  const body = document.getElementById('body-text').value.trim();
  if (!body) {
    showError('Please enter a message body');
    return;
  }

  // Parse headers
  let headers = {};
  try {
    const headersText = document.getElementById('headers-text').value.trim();
    if (headersText) {
      headers = JSON.parse(headersText);
    }
  } catch (error) {
    showError('Invalid headers format. Headers must be valid JSON.');
    return;
  }

  // Get email range
  const startRange = document.getElementById('email-start-input').value.trim();
  const endRange = document.getElementById('email-end-input').value.trim();
  let emailRange = null;

  if (startRange && endRange) {
    emailRange = {
      start: parseInt(startRange),
      end: parseInt(endRange)
    };

    if (isNaN(emailRange.start) || isNaN(emailRange.end) || emailRange.start <= 0 || emailRange.end <= 0 || emailRange.start >= emailRange.end) {
      showError('Please enter a valid email range (start must be less than end and both must be positive numbers)');
      return;
    }
  }

  // Get time interval
  const timeInterval = document.getElementById('time-interval-input').value.trim();
  if (!timeInterval || isNaN(parseInt(timeInterval)) || parseInt(timeInterval) <= 0) {
    showError('Please enter a valid time interval (positive number of seconds)');
    return;
  }

  // Get emails per account (for rotation)
  const emailsPerAccount = document.getElementById('emails-per-account-input').value.trim();
  if (!emailsPerAccount || isNaN(parseInt(emailsPerAccount)) || parseInt(emailsPerAccount) <= 0) {
    showError('Please enter a valid number of emails per account (must be positive)');
    return;
  }

  // Get batch size
  const batchSize = document.getElementById('batch-size-input').value.trim();
  if (!batchSize || isNaN(parseInt(batchSize)) || parseInt(batchSize) <= 0) {
    showError('Please enter a valid batch size (must be positive)');
    return;
  }

  // Get test email interval
  const testEmailInterval = document.getElementById('test-email-interval-input').value.trim();
  if (testEmailInterval && (isNaN(parseInt(testEmailInterval)) || parseInt(testEmailInterval) < 0)) {
    showError('Please enter a valid test email interval (must be 0 or positive)');
    return;
  }

  const clientFile = document.getElementById('client-select').value;
  if (!clientFile) {
    showError('Please select a client file (OAuth credentials)');
    return;
  }

  // Get tracking options
  const enableTracking = document.getElementById('enable-tracking-checkbox') ?
    document.getElementById('enable-tracking-checkbox').checked : false;

  const trackingCampaignId = document.getElementById('campaign-id-input') ?
    document.getElementById('campaign-id-input').value.trim() : '';

  const trackingServerUrl = document.getElementById('tracking-server-url') ?
    document.getElementById('tracking-server-url').value.trim() : '';

  try {
    // Reset tracking data for new campaign
    resetTrackingData();

    // Update tracking data
    if (enableTracking) {
      trackingData.campaignId = trackingCampaignId || 'campaign_' + Math.random().toString(36).substring(2, 15);
      trackingData.trackerUrl = trackingServerUrl || 'localhost:3000';
      trackingData.totalRecipients = 0; // Will be updated during sending
    }

    // Start the campaign
    await window.electron.startEmailCampaign({
      accounts: selectedAccounts,
      subject,
      body,
      headers,
      selectedLists,
      emailRange,
      timeInterval: parseInt(timeInterval),
      emailsPerAccount: parseInt(emailsPerAccount),
      batchSize: parseInt(batchSize),
      testEmailInterval: parseInt(testEmailInterval || "0"),
      testEmails: document.getElementById('test-email-input').value.trim(),
      clientFile,
      enableTracking,
      trackingCampaignId: trackingData.campaignId,
      trackingServerUrl: trackingData.trackerUrl
    });

    // Update UI state
    sendingInProgress = true;
    sendingPaused = false;
    updateSendingControls();

    // Clear progress
    document.getElementById('progress-container').style.display = 'block';
    document.getElementById('progress-bar').style.width = '0%';
    document.getElementById('progress-text').textContent = 'Starting campaign...';
    document.getElementById('progress-details').innerHTML = '';

    // Reset dashboard data for new campaign
    resetDashboardData();

    showSuccess('Email campaign started');
  } catch (error) {
    showError('Failed to start email campaign', error);
  }
}

/**
 * Pause the current email campaign
 */
function pauseEmailCampaign() {
  window.electron.pauseEmailCampaign();
  sendingPaused = true;
  updateSendingControls();
}

/**
 * Resume the current email campaign
 */
function resumeEmailCampaign() {
  window.electron.resumeEmailCampaign();
  sendingPaused = false;
  updateSendingControls();
}

/**
 * Stop the current email campaign
 */
function stopEmailCampaign() {
  if (!confirm('Are you sure you want to stop the current campaign?')) {
    return;
  }

  window.electron.stopEmailCampaign();
  sendingInProgress = false;
  sendingPaused = true;
  updateSendingControls();
}

/**
 * Update the sending controls UI based on the current state
 */
function updateSendingControls() {
  const startBtn = document.getElementById('start-campaign-btn');
  const pauseBtn = document.getElementById('pause-campaign-btn');
  const resumeBtn = document.getElementById('resume-campaign-btn');
  const stopBtn = document.getElementById('stop-campaign-btn');

  if (sendingInProgress) {
    startBtn.disabled = true;
    stopBtn.disabled = false;

    if (sendingPaused) {
      pauseBtn.disabled = true;
      resumeBtn.disabled = false;
    } else {
      pauseBtn.disabled = false;
      resumeBtn.disabled = true;
    }
  } else {
    startBtn.disabled = false;
    pauseBtn.disabled = true;
    resumeBtn.disabled = true;
    stopBtn.disabled = true;
  }
}

/**
 * Update the sending progress UI based on the current state
 * @param {object} data - Progress data from the main process
 */
function updateSendingProgress(data) {
  const progressContainer = document.getElementById('progress-container');
  const progressBar = document.getElementById('progress-bar');
  const progressText = document.getElementById('progress-text');
  const progressDetails = document.getElementById('progress-details');

  // Show progress container
  progressContainer.style.display = 'block';

  // Update progress bar
  progressBar.style.width = `${data.progress}%`;

  // Update progress text
  progressText.textContent = data.message || '';

  // Add details if provided
  if (data.details) {
    const detailElem = document.createElement('div');
    detailElem.className = data.isSuccess ? 'success' : 'error';
    detailElem.textContent = data.details;

    // Prepend to keep most recent at top
    progressDetails.insertBefore(detailElem, progressDetails.firstChild);

    // Trim if too many entries
    while (progressDetails.children.length > 100) {
      progressDetails.removeChild(progressDetails.lastChild);
    }
  }

  // Update dashboard
  updateDashboard(data);
}

// ============================================================
// History Management Functions
// ============================================================

/**
 * Save current settings to history
 */
async function saveCurrentAsHistory() {
  try {
    const historyData = {
      from: document.getElementById('from-name-input').value.trim(),
      subject: document.getElementById('subject-input').value.trim(),
      body: document.getElementById('body-text').value.trim(),
      headers: JSON.parse(document.getElementById('headers-text').value.trim() || '{}'),
      servers: selectedAccounts,
      selected_lists: selectedLists,
      email_range: document.getElementById('email-start-input').value && document.getElementById('email-end-input').value
        ? `${document.getElementById('email-start-input').value}-${document.getElementById('email-end-input').value}`
        : '',
      time_interval: document.getElementById('time-interval-input').value,
      emails_per_account: document.getElementById('emails-per-account-input').value,
      batch_size: document.getElementById('batch-size-input').value,
      test_emails: document.getElementById('test-email-input').value,
      test_email_interval: document.getElementById('test-email-interval-input').value,
      timestamp: new Date().toISOString()
    };

    const result = await window.electron.saveHistory(historyData);

    if (result.success) {
      showSuccess('Campaign settings saved to history');
    } else {
      showError('Failed to save history', result.error);
    }
  } catch (error) {
    showError('Failed to save history', error);
  }
}

/**
 * Load settings from history
 */
async function loadHistory() {
  try {
    const result = await window.electron.loadHistory();

    if (result.success) {
      const historyData = result.data;

      // Populate the fields with the loaded data
      document.getElementById('from-name-input').value = historyData.from || '';
      document.getElementById('subject-input').value = historyData.subject || '';
      document.getElementById('headers-text').value = JSON.stringify(historyData.headers || {}, null, 2);
      document.getElementById('body-text').value = historyData.body || '';

      // Load selected accounts
      const accountListbox = document.getElementById('account-listbox');
      accountListbox.querySelectorAll('option').forEach(option => {
        option.selected = historyData.servers && historyData.servers.includes(option.value);
      });
      updateSelectedAccounts();

      // Load selected lists
      const listListbox = document.getElementById('list-listbox');
      listListbox.querySelectorAll('option').forEach(option => {
        const listName = option.value;
        option.selected = historyData.selected_lists && historyData.selected_lists.includes(listName);
      });
      updateSelectedLists();

      // Load email range
      if (historyData.email_range) {
        const [start, end] = historyData.email_range.split('-');
        document.getElementById('email-start-input').value = start || '';
        document.getElementById('email-end-input').value = end || '';
      }

      // Load time interval
      document.getElementById('time-interval-input').value = historyData.time_interval || '';

      // Load rotation settings if they exist
      document.getElementById('emails-per-account-input').value = historyData.emails_per_account || '10';
      document.getElementById('batch-size-input').value = historyData.batch_size || '1';

      // Load test emails
      document.getElementById('test-email-input').value = historyData.test_emails || '';

      // Load test email interval
      document.getElementById('test-email-interval-input').value = historyData.test_email_interval || '0';

      showSuccess('History loaded successfully');
    } else {
      showError('Failed to load history', result.error);
    }
  } catch (error) {
    showError('Failed to load history', error);
  }
}

// ============================================================
// Utility Functions
// ============================================================

/**
 * Validate email address format
 * @param {string} email - Email address to validate
 * @returns {boolean} - True if email is valid
 */
function isValidEmail(email) {
  const re = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return re.test(email);
}

/**
 * Show an error message
 * @param {string} message - Error message
 * @param {string} details - Additional error details
 */
function showError(message, details = '') {
  const errorContainer = document.getElementById('error-message');

  // Handle multi-line messages
  if (message.includes('\n')) {
    errorContainer.innerHTML = message.replace(/\n/g, '<br>');
    if (details) {
      errorContainer.innerHTML += '<br>' + (details ? ': ' + details.replace(/\n/g, '<br>') : '');
    }
  } else {
    errorContainer.textContent = message + (details ? ': ' + details : '');
  }

  errorContainer.style.display = 'block';

  // Auto-hide after 15 seconds for longer messages
  setTimeout(() => {
    errorContainer.style.display = 'none';
  }, 15000);

  // Also log to console
  console.error(message, details);
}

/**
 * Show a success message
 * @param {string} message - Success message
 */
function showSuccess(message) {
  const successContainer = document.getElementById('success-message');

  // Handle multi-line messages
  if (message.includes('\n')) {
    successContainer.innerHTML = message.replace(/\n/g, '<br>');
  } else {
    successContainer.textContent = message;
  }

  successContainer.style.display = 'block';

  // Auto-hide after 10 seconds for longer messages
  setTimeout(() => {
    successContainer.style.display = 'none';
  }, 10000);

  // Also log to console
  console.log(message);
}

// ============================================================
// Dashboard Functions
// ============================================================

/**
 * Initialize the dashboard charts and tables
 */
function initializeDashboard() {
  // Initialize the sending rate chart
  initializeSendingRateChart();

  // Initialize the success vs errors chart
  initializeSuccessErrorChart();

  // Initialize tracking charts
  initializeTrackingCharts();

  // Reset dashboard data
  resetDashboardData();

  // Reset tracking data
  resetTrackingData();
}

/**
 * Reset dashboard data to initial state
 */
function resetDashboardData() {
  dashboardData.totalEmails = 0;
  dashboardData.successCount = 0;
  dashboardData.errorCount = 0;
  dashboardData.accountPerformance = {};
  dashboardData.timePoints = [];
  dashboardData.sentCountPoints = [];
  dashboardData.successPoints = [];
  dashboardData.errorPoints = [];

  // Update UI
  document.getElementById('dashboard-total-emails').textContent = '0';
  document.getElementById('dashboard-success').textContent = '0';
  document.getElementById('dashboard-errors').textContent = '0';
  document.getElementById('dashboard-success-rate').textContent = '0%';

  // Clear account performance table
  document.getElementById('account-performance-table').innerHTML = '';

  // Clear recent activity log
  document.getElementById('recent-activity-log').innerHTML = '';
}

/**
 * Initialize the sending rate chart
 */
function initializeSendingRateChart() {
  const ctx = document.getElementById('sending-rate-chart').getContext('2d');

  // Create the chart
  window.sendingRateChart = new Chart(ctx, {
    type: 'line',
    data: {
      labels: [],
      datasets: [{
        label: 'Emails Sent',
        data: [],
        borderColor: 'rgba(54, 162, 235, 1)',
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        borderWidth: 2,
        fill: true,
        tension: 0.4
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
        },
        tooltip: {
          mode: 'index',
          intersect: false
        }
      },
      scales: {
        x: {
          display: true,
          title: {
            display: true,
            text: 'Time'
          }
        },
        y: {
          display: true,
          title: {
            display: true,
            text: 'Emails Sent'
          },
          beginAtZero: true
        }
      }
    }
  });
}

/**
 * Initialize the success vs errors chart
 */
function initializeSuccessErrorChart() {
  const ctx = document.getElementById('success-error-chart').getContext('2d');

  // Create the chart
  window.successErrorChart = new Chart(ctx, {
    type: 'line',
    data: {
      labels: [],
      datasets: [
        {
          label: 'Success',
          data: [],
          borderColor: 'rgba(75, 192, 192, 1)',
          backgroundColor: 'rgba(75, 192, 192, 0.2)',
          borderWidth: 2,
          fill: true,
          tension: 0.4
        },
        {
          label: 'Errors',
          data: [],
          borderColor: 'rgba(255, 99, 132, 1)',
          backgroundColor: 'rgba(255, 99, 132, 0.2)',
          borderWidth: 2,
          fill: true,
          tension: 0.4
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
        },
        tooltip: {
          mode: 'index',
          intersect: false
        }
      },
      scales: {
        x: {
          display: true,
          title: {
            display: true,
            text: 'Time'
          }
        },
        y: {
          display: true,
          title: {
            display: true,
            text: 'Count'
          },
          beginAtZero: true
        }
      }
    }
  });
}

/**
 * Update dashboard data and UI based on send progress
 * @param {object} data - Progress data from the main process
 */
function updateDashboard(data) {
  // Add to recent activity log
  if (data.details) {
    addActivityLog(data);
  }

  // Update metrics based on data type
  if (data.status === 'starting') {
    resetDashboardData();
    dashboardData.totalEmails = data.total || 0;
    document.getElementById('dashboard-total-emails').textContent = dashboardData.totalEmails.toString();
  }
  else if (data.status === 'sending' && data.isSuccess) {
    // Increment success count
    dashboardData.successCount++;
    document.getElementById('dashboard-success').textContent = dashboardData.successCount.toString();

    // Update account performance
    updateAccountPerformance(data.details.split('→')[0].trim(), true);

    // Add data point for charts
    updateChartDataPoints(true);
  }
  else if (data.status === 'error') {
    // Increment error count
    dashboardData.errorCount++;
    document.getElementById('dashboard-errors').textContent = dashboardData.errorCount.toString();

    // Update account performance if available
    if (data.details && data.details.includes('→')) {
      updateAccountPerformance(data.details.split('→')[0].trim(), false);
    }

    // Add data point for charts
    updateChartDataPoints(false);
  }

  // Update success rate
  const total = dashboardData.successCount + dashboardData.errorCount;
  const successRate = total > 0 ? Math.round((dashboardData.successCount / total) * 100) : 0;
  document.getElementById('dashboard-success-rate').textContent = `${successRate}%`;
}

/**
 * Add an entry to the activity log
 * @param {object} data - Progress data
 */
function addActivityLog(data) {
  const logElement = document.getElementById('recent-activity-log');

  // Create log entry
  const logEntry = document.createElement('div');
  logEntry.className = data.isSuccess ? 'text-success' : 'text-danger';

  // Add timestamp
  const now = new Date();
  const timestamp = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;

  logEntry.innerHTML = `<small class="text-muted">[${timestamp}]</small> ${data.details}`;

  // Add to log (at the top)
  logElement.insertBefore(logEntry, logElement.firstChild);

  // Trim if too many entries
  while (logElement.children.length > 100) {
    logElement.removeChild(logElement.lastChild);
  }
}

/**
 * Update account performance data and table
 * @param {string} account - Email account
 * @param {boolean} success - Whether the send was successful
 */
function updateAccountPerformance(account, success) {
  // Initialize account if not exists
  if (!dashboardData.accountPerformance[account]) {
    dashboardData.accountPerformance[account] = {
      sent: 0,
      success: 0,
      errors: 0
    };
  }

  // Update metrics
  dashboardData.accountPerformance[account].sent++;
  if (success) {
    dashboardData.accountPerformance[account].success++;
  } else {
    dashboardData.accountPerformance[account].errors++;
  }

  // Update table
  updateAccountPerformanceTable();
}

/**
 * Update the account performance table
 */
function updateAccountPerformanceTable() {
  const tableBody = document.getElementById('account-performance-table');
  tableBody.innerHTML = '';

  // Add a row for each account
  for (const [account, stats] of Object.entries(dashboardData.accountPerformance)) {
    const row = document.createElement('tr');

    // Calculate success rate
    const successRate = Math.round((stats.success / stats.sent) * 100);

    // Determine status
    let status = 'Active';
    let statusClass = 'text-success';
    if (stats.errors > stats.success) {
      status = 'Warning';
      statusClass = 'text-warning';
    }
    if (stats.errors > stats.success * 2) {
      status = 'Critical';
      statusClass = 'text-danger';
    }

    // Create row content
    row.innerHTML = `
      <td>${account}</td>
      <td>${stats.sent}</td>
      <td>${stats.success}</td>
      <td>${stats.errors}</td>
      <td>${successRate}%</td>
      <td class="${statusClass}">${status}</td>
    `;

    tableBody.appendChild(row);
  }
}

/**
 * Update chart data points
 * @param {boolean} success - Whether the send was successful
 */
function updateChartDataPoints(success) {
  // Get current time for x-axis
  const now = new Date();
  const timeLabel = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;

  // Add to time points array if we don't have too many
  if (dashboardData.timePoints.length < 20) {
    dashboardData.timePoints.push(timeLabel);
  } else {
    // Shift arrays to keep only the most recent 20 points
    dashboardData.timePoints.shift();
    dashboardData.timePoints.push(timeLabel);
    dashboardData.sentCountPoints.shift();
    dashboardData.successPoints.shift();
    dashboardData.errorPoints.shift();
  }

  // Update sent count points
  const totalSent = dashboardData.successCount + dashboardData.errorCount;
  dashboardData.sentCountPoints.push(totalSent);

  // Update success/error points
  dashboardData.successPoints.push(dashboardData.successCount);
  dashboardData.errorPoints.push(dashboardData.errorCount);

  // Update charts
  updateCharts();
}

/**
 * Update all charts with current data
 */
function updateCharts() {
  // Update sending rate chart
  if (window.sendingRateChart) {
    window.sendingRateChart.data.labels = dashboardData.timePoints;
    window.sendingRateChart.data.datasets[0].data = dashboardData.sentCountPoints;
    window.sendingRateChart.update();
  }

  // Update success/error chart
  if (window.successErrorChart) {
    window.successErrorChart.data.labels = dashboardData.timePoints;
    window.successErrorChart.data.datasets[0].data = dashboardData.successPoints;
    window.successErrorChart.data.datasets[1].data = dashboardData.errorPoints;
    window.successErrorChart.update();
  }

  // Update tracking charts
  updateTrackingCharts();
}

// ============================================================
// Email Tracking Functions
// ============================================================

/**
 * Reset tracking data to initial state
 */
function resetTrackingData() {
  // Reset open tracking data
  trackingData.opens.total = 0;
  trackingData.opens.unique = 0;
  trackingData.opens.byEmail = {};
  trackingData.opens.timePoints = [];
  trackingData.opens.countPoints = [];
  trackingData.opens.recentActivity = [];

  // Reset click tracking data
  trackingData.clicks.total = 0;
  trackingData.clicks.unique = 0;
  trackingData.clicks.byEmail = {};
  trackingData.clicks.byLink = {};
  trackingData.clicks.timePoints = [];
  trackingData.clicks.countPoints = [];
  trackingData.clicks.recentActivity = [];

  // Reset campaign data
  trackingData.campaignId = '';
  trackingData.trackerUrl = '';
  trackingData.totalRecipients = 0;

  // Update UI
  document.getElementById('total-opens-count').textContent = '0';
  document.getElementById('unique-opens-count').textContent = '0';
  document.getElementById('open-rate-percentage').textContent = '0%';
  document.getElementById('total-clicks-count').textContent = '0';
  document.getElementById('unique-clicks-count').textContent = '0';
  document.getElementById('click-rate-percentage').textContent = '0%';

  // Clear tables
  document.getElementById('opens-table-body').innerHTML = '';
  document.getElementById('clicks-table-body').innerHTML = '';
  document.getElementById('links-table-body').innerHTML = '';
}

/**
 * Initialize tracking charts
 */
function initializeTrackingCharts() {
  // Initialize opens chart
  const opensCtx = document.getElementById('opens-chart').getContext('2d');

  window.opensChart = new Chart(opensCtx, {
    type: 'line',
    data: {
      labels: [],
      datasets: [{
        label: 'Email Opens',
        data: [],
        borderColor: 'rgba(75, 192, 192, 1)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        borderWidth: 2,
        fill: true,
        tension: 0.4
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
        },
        tooltip: {
          mode: 'index',
          intersect: false
        }
      },
      scales: {
        x: {
          display: true,
          title: {
            display: true,
            text: 'Time'
          }
        },
        y: {
          display: true,
          title: {
            display: true,
            text: 'Opens'
          },
          beginAtZero: true
        }
      }
    }
  });

  // Initialize clicks chart
  const clicksCtx = document.getElementById('clicks-chart').getContext('2d');

  window.clicksChart = new Chart(clicksCtx, {
    type: 'line',
    data: {
      labels: [],
      datasets: [{
        label: 'Link Clicks',
        data: [],
        borderColor: 'rgba(54, 162, 235, 1)',
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        borderWidth: 2,
        fill: true,
        tension: 0.4
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
        },
        tooltip: {
          mode: 'index',
          intersect: false
        }
      },
      scales: {
        x: {
          display: true,
          title: {
            display: true,
            text: 'Time'
          }
        },
        y: {
          display: true,
          title: {
            display: true,
            text: 'Clicks'
          },
          beginAtZero: true
        }
      }
    }
  });
}

/**
 * Update tracking charts
 */
function updateTrackingCharts() {
  // Update opens chart
  if (window.opensChart) {
    window.opensChart.data.labels = trackingData.opens.timePoints;
    window.opensChart.data.datasets[0].data = trackingData.opens.countPoints;
    window.opensChart.update();
  }

  // Update clicks chart
  if (window.clicksChart) {
    window.clicksChart.data.labels = trackingData.clicks.timePoints;
    window.clicksChart.data.datasets[0].data = trackingData.clicks.countPoints;
    window.clicksChart.update();
  }
}

/**
 * Track an email open event
 * @param {object} data - Open tracking data {email, time, device, ip}
 */
function trackEmailOpen(data) {
  // Increment total opens count
  trackingData.opens.total++;

  // Update unique opens count if this is the first open for this email
  if (!trackingData.opens.byEmail[data.email]) {
    trackingData.opens.byEmail[data.email] = {
      count: 0,
      lastOpened: new Date()
    };
    trackingData.opens.unique++;
  }

  // Update this email's open count
  trackingData.opens.byEmail[data.email].count++;
  trackingData.opens.byEmail[data.email].lastOpened = new Date();

  // Add to recent activity
  trackingData.opens.recentActivity.unshift({
    email: data.email,
    time: new Date(),
    device: data.device || 'Unknown',
    ip: data.ip || 'Unknown'
  });

  // Limit the size of recent activity
  if (trackingData.opens.recentActivity.length > 100) {
    trackingData.opens.recentActivity.pop();
  }

  // Add data point for chart
  const now = new Date();
  const timeLabel = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;

  // Update chart data
  if (trackingData.opens.timePoints.length < 20) {
    trackingData.opens.timePoints.push(timeLabel);
    trackingData.opens.countPoints.push(trackingData.opens.total);
  } else {
    trackingData.opens.timePoints.shift();
    trackingData.opens.timePoints.push(timeLabel);
    trackingData.opens.countPoints.shift();
    trackingData.opens.countPoints.push(trackingData.opens.total);
  }

  // Update UI
  updateTrackingUI();
}

/**
 * Track a link click event
 * @param {object} data - Click tracking data {email, link, time, device}
 */
function trackLinkClick(data) {
  // Increment total clicks count
  trackingData.clicks.total++;

  // Update unique clicks count if this is the first click for this email
  if (!trackingData.clicks.byEmail[data.email]) {
    trackingData.clicks.byEmail[data.email] = {
      count: 0,
      lastClicked: new Date(),
      links: {}
    };
    trackingData.clicks.unique++;
  }

  // Update this email's click count
  trackingData.clicks.byEmail[data.email].count++;
  trackingData.clicks.byEmail[data.email].lastClicked = new Date();

  // Update this email's clicked links
  if (!trackingData.clicks.byEmail[data.email].links[data.link]) {
    trackingData.clicks.byEmail[data.email].links[data.link] = 0;
  }
  trackingData.clicks.byEmail[data.email].links[data.link]++;

  // Update link stats
  if (!trackingData.clicks.byLink[data.link]) {
    trackingData.clicks.byLink[data.link] = {
      count: 0,
      uniqueCount: 0,
      lastClicked: new Date(),
      emails: {}
    };
  }

  // Update this link's click count
  trackingData.clicks.byLink[data.link].count++;
  trackingData.clicks.byLink[data.link].lastClicked = new Date();

  // Update unique clicks for this link
  if (!trackingData.clicks.byLink[data.link].emails[data.email]) {
    trackingData.clicks.byLink[data.link].emails[data.email] = true;
    trackingData.clicks.byLink[data.link].uniqueCount++;
  }

  // Add to recent activity
  trackingData.clicks.recentActivity.unshift({
    email: data.email,
    link: data.link,
    time: new Date(),
    device: data.device || 'Unknown'
  });

  // Limit the size of recent activity
  if (trackingData.clicks.recentActivity.length > 100) {
    trackingData.clicks.recentActivity.pop();
  }

  // Add data point for chart
  const now = new Date();
  const timeLabel = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;

  // Update chart data
  if (trackingData.clicks.timePoints.length < 20) {
    trackingData.clicks.timePoints.push(timeLabel);
    trackingData.clicks.countPoints.push(trackingData.clicks.total);
  } else {
    trackingData.clicks.timePoints.shift();
    trackingData.clicks.timePoints.push(timeLabel);
    trackingData.clicks.countPoints.shift();
    trackingData.clicks.countPoints.push(trackingData.clicks.total);
  }

  // Update UI
  updateTrackingUI();
}

/**
 * Update tracking metrics UI
 */
function updateTrackingUI() {
  // Update opens metrics
  document.getElementById('total-opens-count').textContent = trackingData.opens.total.toString();
  document.getElementById('unique-opens-count').textContent = trackingData.opens.unique.toString();

  // Calculate open rate
  const openRate = trackingData.totalRecipients > 0
    ? Math.round((trackingData.opens.unique / trackingData.totalRecipients) * 100)
    : 0;
  document.getElementById('open-rate-percentage').textContent = `${openRate}%`;

  // Update clicks metrics
  document.getElementById('total-clicks-count').textContent = trackingData.clicks.total.toString();
  document.getElementById('unique-clicks-count').textContent = trackingData.clicks.unique.toString();

  // Calculate click rate
  const clickRate = trackingData.totalRecipients > 0
    ? Math.round((trackingData.clicks.unique / trackingData.totalRecipients) * 100)
    : 0;
  document.getElementById('click-rate-percentage').textContent = `${clickRate}%`;

  // Update opens table
  updateOpensTable();

  // Update clicks tables
  updateClicksTable();
  updateLinksTable();

  // Update charts
  updateTrackingCharts();
}

/**
 * Update the opens table with recent data
 */
function updateOpensTable() {
  const tableBody = document.getElementById('opens-table-body');
  tableBody.innerHTML = '';

  // Add recent opens to the table
  trackingData.opens.recentActivity.slice(0, 10).forEach(activity => {
    const row = document.createElement('tr');
    const time = new Date(activity.time);
    const formattedTime = `${time.toLocaleDateString()} ${time.toLocaleTimeString()}`;

    row.innerHTML = `
      <td>${activity.email}</td>
      <td>${formattedTime}</td>
      <td>${activity.device}</td>
      <td>${activity.ip}</td>
    `;

    tableBody.appendChild(row);
  });
}

/**
 * Update the clicks table with recent data
 */
function updateClicksTable() {
  const tableBody = document.getElementById('clicks-table-body');
  tableBody.innerHTML = '';

  // Add recent clicks to the table
  trackingData.clicks.recentActivity.slice(0, 10).forEach(activity => {
    const row = document.createElement('tr');
    const time = new Date(activity.time);
    const formattedTime = `${time.toLocaleDateString()} ${time.toLocaleTimeString()}`;
    const truncatedLink = activity.link.length > 30
      ? activity.link.substring(0, 30) + '...'
      : activity.link;

    row.innerHTML = `
      <td>${activity.email}</td>
      <td title="${activity.link}">${truncatedLink}</td>
      <td>${formattedTime}</td>
      <td>${activity.device}</td>
    `;

    tableBody.appendChild(row);
  });
}

/**
 * Update the links table with popular links
 */
function updateLinksTable() {
  const tableBody = document.getElementById('links-table-body');
  tableBody.innerHTML = '';

  // Get links sorted by click count
  const sortedLinks = Object.entries(trackingData.clicks.byLink)
    .sort((a, b) => b[1].count - a[1].count);

  // Add popular links to the table
  sortedLinks.slice(0, 10).forEach(([link, stats]) => {
    const row = document.createElement('tr');
    const truncatedLink = link.length > 30 ? link.substring(0, 30) + '...' : link;
    const time = new Date(stats.lastClicked);
    const formattedTime = `${time.toLocaleDateString()} ${time.toLocaleTimeString()}`;

    row.innerHTML = `
      <td title="${link}">${truncatedLink}</td>
      <td>${stats.count}</td>
      <td>${stats.uniqueCount}</td>
      <td>${formattedTime}</td>
    `;

    tableBody.appendChild(row);
  });
}

/**
 * Generate a tracking pixel for an email
 * @param {string} email - Recipient's email address
 * @param {string} campaignId - Campaign identifier
 * @returns {string} - HTML for the tracking pixel
 */
function generateTrackingPixel(email, campaignId) {
  const trackerUrl = document.getElementById('tracking-server-url').value.trim() ||
                    window.location.origin;

  const pixelUrl = `${trackerUrl}/track/open?email=${encodeURIComponent(email)}&campaign=${encodeURIComponent(campaignId)}&t=${Date.now()}`;

  return `<img src="${pixelUrl}" width="1" height="1" alt="" style="display:none;">`;
}

/**
 * Process HTML body to add tracking for all links
 * @param {string} html - Original HTML content
 * @param {string} email - Recipient's email address
 * @param {string} campaignId - Campaign identifier
 * @returns {string} - HTML with tracked links
 */
function processLinksForTracking(html, email, campaignId) {
  const trackerUrl = document.getElementById('tracking-server-url').value.trim() ||
                    window.location.origin;

  // Create a temporary element to parse the HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;

  // Process all links
  const links = tempDiv.querySelectorAll('a[href]');
  links.forEach(link => {
    const originalUrl = link.getAttribute('href');
    if (!originalUrl || originalUrl.startsWith('#') || originalUrl.startsWith('javascript:')) {
      return; // Skip anchors and javascript links
    }

    // Create tracking URL
    const trackingUrl = `${trackerUrl}/track/click?email=${encodeURIComponent(email)}&campaign=${encodeURIComponent(campaignId)}&url=${encodeURIComponent(originalUrl)}&t=${Date.now()}`;

    // Replace the original URL with the tracking URL
    link.setAttribute('href', trackingUrl);
  });

  return tempDiv.innerHTML;
}

/**
 * Add tracking to an email body
 * @param {string} html - Original HTML content
 * @param {string} email - Recipient's email address
 * @returns {string} - HTML with tracking pixel and tracked links
 */
function addTrackingToEmail(html, email) {
  // Return original HTML if tracking is disabled
  if (!document.getElementById('enable-tracking').checked) {
    return html;
  }

  // Get campaign ID
  const campaignId = document.getElementById('tracking-campaign-id').value.trim() ||
                    generateRandomCampaignId();

  // Save campaign data
  trackingData.campaignId = campaignId;
  trackingData.trackerUrl = document.getElementById('tracking-server-url').value.trim() ||
                          window.location.origin;

  // Process HTML for link tracking
  let processedHtml = processLinksForTracking(html, email, campaignId);

  // Add tracking pixel at the end of the email
  const trackingPixel = generateTrackingPixel(email, campaignId);
  processedHtml += trackingPixel;

  return processedHtml;
}

/**
 * Generate a random campaign ID if none is provided
 * @returns {string} - Random campaign ID
 */
function generateRandomCampaignId() {
  return 'campaign_' + Math.random().toString(36).substring(2, 15) +
         Math.random().toString(36).substring(2, 15);
}
