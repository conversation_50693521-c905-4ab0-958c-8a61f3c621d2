# Email Headers Guide

## Overview
Custom email headers allow you to add additional metadata and instructions to your emails. Headers must be in valid JSON format.

## Basic Format
```json
{
  "Header-Name": "Header Value",
  "Another-Header": "Another Value"
}
```

## Common Email Headers

### 1. Reply-To
Sets where replies should be sent:
```json
{
  "Reply-To": "<EMAIL>"
}
```

### 2. Priority Settings
Set email priority (1=High, 3=Normal, 5=Low):
```json
{
  "X-Priority": "1",
  "Importance": "high"
}
```

### 3. Unsubscribe Headers
Required for marketing emails:
```json
{
  "List-Unsubscribe": "<mailto:<EMAIL>>",
  "List-Unsubscribe-Post": "List-Unsubscribe=One-Click"
}
```

### 4. Custom Mailer Identification
```json
{
  "X-Mailer": "Your Email Marketing App v1.0"
}
```

### 5. Return Path (Boun<PERSON> Handling)
```json
{
  "Return-Path": "<EMAIL>"
}
```

**Important Note about Return-Path with Gmail API:**
- Gmail may override Return-Path with the authenticated sender's domain
- For best results, use an email address on the same domain as your sender
- This is where bounce notifications will be sent

## Complete Marketing Email Example
```json
{
  "Reply-To": "<EMAIL>",
  "List-Unsubscribe": "<mailto:<EMAIL>>",
  "List-Unsubscribe-Post": "List-Unsubscribe=One-Click",
  "X-Mailer": "Email Marketing App",
  "X-Priority": "3",
  "Return-Path": "<EMAIL>"
}
```

## Tag Support in Headers
Headers support the same tags as other fields:
```json
{
  "Reply-To": "support-[an_5]@yourdomain.com",
  "X-Campaign-ID": "campaign-[current_date]-[random_string]"
}
```

## Common Mistakes to Avoid

### ❌ Wrong: Not using quotes
```json
{
  Reply-To: <EMAIL>
}
```

### ✅ Correct: Use quotes for both keys and values
```json
{
  "Reply-To": "<EMAIL>"
}
```

### ❌ Wrong: Using arrays instead of objects
```json
[
  "Reply-To: <EMAIL>"
]
```

### ✅ Correct: Use object format
```json
{
  "Reply-To": "<EMAIL>"
}
```

## Using the App Features

1. **Presets**: Use the dropdown to quickly add common headers
2. **Validate**: Click the "Validate" button to check your JSON format
3. **Clear**: Use "Clear Headers" to start over

## Headers That Will Appear in Gmail

When you send emails with custom headers, recipients will see:
- **Reply-To**: Changes the reply address
- **List-Unsubscribe**: Adds unsubscribe option in Gmail
- **X-Priority**: May show priority indicators
- **Custom headers**: Visible in email source/raw view

## Testing Headers

1. Send a test email to yourself
2. In Gmail, click "Show original" or "View source"
3. Look for your custom headers in the email source

## Important Notes

- Headers are case-insensitive but conventionally use Title-Case
- Some headers may be filtered by email providers
- Always test headers with your email provider
- Marketing emails should include unsubscribe headers for compliance

## Return-Path Special Considerations

### ⚠️ Gmail API Limitation - IMPORTANT!
**Gmail API automatically sets Return-Path and you CANNOT override it.**

When you add a custom Return-Path header, you'll get **TWO Return-Path headers**:
1. Gmail's automatic one: `Return-Path: <<EMAIL>>`
2. Your custom one: `Return-Path: <EMAIL>`

### ✅ Solution: Use Alternative Bounce Headers
Instead of Return-Path, use these headers that Gmail respects:

```json
{
  "Errors-To": "<EMAIL>",
  "Return-Receipt-To": "<EMAIL>"
}
```

### Why This Works Better
- **Errors-To**: Many mail servers use this for bounce notifications
- **Return-Receipt-To**: Used for delivery receipts and bounce handling
- **No Conflicts**: These don't conflict with Gmail's automatic headers

### Updated Best Practice
```json
{
  "Reply-To": "<EMAIL>",
  "Errors-To": "<EMAIL>",
  "Return-Receipt-To": "<EMAIL>",
  "List-Unsubscribe": "<mailto:<EMAIL>>"
}
```

### Testing Bounce Handling
1. Use "Bounce Handling (Gmail API)" preset
2. Send test email to invalid address
3. Check if bounces arrive at your Errors-To address
4. Monitor both Gmail account and your custom bounce address
