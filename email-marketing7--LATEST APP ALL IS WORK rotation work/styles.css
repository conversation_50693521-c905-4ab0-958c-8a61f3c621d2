/* General styles */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: #f8f9fa;
  padding-bottom: 50px;
}

/* MH-SENDER Branding */
.app-title {
  padding: 20px 0;
  margin-bottom: 20px;
  position: relative;
}

#brand-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 3.5rem;
  font-weight: 800;
  letter-spacing: 1px;
  color: #2a49a5;
  margin-bottom: 0;
  text-transform: uppercase;
  background: linear-gradient(90deg, #1a237e, #0d47a1, #1976d2, #0288d1);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradient-animation 5s ease infinite;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.app-subtitle {
  font-size: 1.1rem;
  color: #455a64;
  margin-top: 5px;
  opacity: 0.9;
  animation: fadeIn 1.5s;
}

@keyframes gradient-animation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 0.9;
    transform: translateY(0);
  }
}

/* Card styles */
.card {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
  background-color: #f1f3f5;
  border-bottom: 1px solid #dee2e6;
  padding: 0.75rem 1rem;
}

/* Form controls */
.form-control, .form-select {
  border-radius: 6px;
}

.form-control:focus, .form-select:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Buttons */
.btn {
  border-radius: 6px;
  font-weight: 500;
}

/* Enhanced Navigation */
.nav-tabs {
  border-bottom: 2px solid #dee2e6;
  background-color: #fbfbfd;
  border-radius: 8px 8px 0 0;
  padding: 0.5rem 0.5rem 0 0.5rem;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.nav-tabs .nav-item {
  margin-bottom: -2px;
}

.nav-tabs .nav-link {
  border: none;
  border-bottom: 2px solid transparent;
  border-radius: 6px 6px 0 0;
  color: #6c757d;
  font-weight: 500;
  padding: 0.75rem 1.25rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.nav-tabs .nav-link:hover {
  color: #1976d2;
  background-color: rgba(25, 118, 210, 0.05);
  border-color: rgba(25, 118, 210, 0.1);
}

.nav-tabs .nav-link.active {
  color: #1976d2;
  background-color: rgba(25, 118, 210, 0.08);
  border-color: #1976d2;
  font-weight: 600;
}

.nav-tabs .nav-link i {
  font-size: 1.1rem;
}

/* Modal styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1050;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.4);
}

.modal-dialog {
  margin: 50px auto;
  max-width: 600px;
}

.modal-content {
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* Progress styles */
#progress-details {
  font-family: monospace;
  font-size: 0.85rem;
  line-height: 1.5;
}

#progress-details .success {
  color: #28a745;
}

#progress-details .error {
  color: #dc3545;
}

/* List boxes */
select[multiple] {
  height: auto;
}

/* Alerts */
.alert {
  border-radius: 6px;
  font-weight: 500;
}

/* Tags help button */
.position-fixed.bottom-0.end-0 {
  z-index: 1000;
}

/* Help bubble system */
.help-bubble-container {
  position: relative;
  display: inline-block;
}

.help-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #6c757d;
  color: white;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  margin-left: 5px;
  transition: background-color 0.2s ease;
}

.help-icon:hover {
  background-color: #17a2b8;
}

.help-bubble {
  position: absolute;
  width: 280px;
  background-color: #fff;
  border: 2px solid #17a2b8;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.2);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
  font-size: 14px;
  left: 30px;
  top: -10px;
}

.help-bubble.right {
  left: auto;
  right: 30px;
}

.help-bubble.top {
  bottom: 30px;
  top: auto;
}

.help-bubble.active {
  opacity: 1;
  visibility: visible;
}

.help-bubble::before {
  content: '';
  position: absolute;
  left: -10px;
  top: 15px;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-right: 10px solid #17a2b8;
}

.help-bubble.right::before {
  left: auto;
  right: -10px;
  border-right: none;
  border-left: 10px solid #17a2b8;
}

.help-bubble.top::before {
  top: auto;
  bottom: -10px;
  left: 15px;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid #17a2b8;
  border-bottom: none;
}

.help-character {
  width: 70px;
  height: 70px;
  float: right;
  margin-left: 10px;
  margin-bottom: 5px;
  background-color: #f8f9fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.help-character svg {
  width: 60px;
  height: 60px;
}

.help-title {
  font-weight: bold;
  margin-bottom: 8px;
  color: #17a2b8;
}

.help-content {
  color: #495057;
  line-height: 1.4;
}

.help-close {
  position: absolute;
  top: 5px;
  right: 5px;
  color: #6c757d;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.help-close:hover {
  background-color: #f8f9fa;
  color: #dc3545;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .card {
    margin-bottom: 20px;
  }
  
  .btn {
    padding: 0.375rem 0.5rem;
  }
  
  .btn-group > .btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.875rem;
  }
  
  .help-bubble {
    width: 240px;
  }
}
