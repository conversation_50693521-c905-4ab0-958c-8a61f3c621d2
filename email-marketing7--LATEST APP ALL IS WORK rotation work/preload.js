/**
 * Preload Script for Email Marketing Application
 * This script creates a secure bridge between the main process and renderer process
 */

const { contextBridge, ipcRenderer } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electron', {
  // Account management
  loadAccounts: () => ipcRenderer.invoke('load-accounts'),
  saveAccount: (account) => ipcRenderer.invoke('save-account', account),
  deleteAccount: (account) => ipcRenderer.invoke('delete-account', account),

  // Email list management
  loadEmailLists: () => ipcRenderer.invoke('load-email-lists'),
  createEmailList: (data) => ipcRenderer.invoke('create-email-list', data),
  deleteEmailList: (name) => ipcRenderer.invoke('delete-email-list', name),
  getEmailListContent: (name) => ipcRenderer.invoke('get-email-list-content', name),

  // History management
  saveHistory: (data) => ipcRenderer.invoke('save-history', data),
  loadHistory: () => ipcRenderer.invoke('load-history'),

  // Client file management
  getClientFiles: () => ipcRenderer.invoke('get-client-files'),
  uploadClientFile: () => ipcRenderer.invoke('upload-client-file'),
  deleteClientFile: (filename) => ipcRenderer.invoke('delete-client-file', filename),
  getAccountClientFile: (account) => ipcRenderer.invoke('get-account-client-file', account),

  // Email sending
  sendTestEmail: (data) => ipcRenderer.invoke('send-test-email', data),
  startEmailCampaign: (data) => ipcRenderer.invoke('start-email-campaign', data),
  pauseEmailCampaign: () => ipcRenderer.send('pause-email-campaign'),
  resumeEmailCampaign: () => ipcRenderer.send('resume-email-campaign'),
  stopEmailCampaign: () => ipcRenderer.send('stop-email-campaign'),

  // Email tracking
  trackEmailOpen: (data) => ipcRenderer.invoke('track-email-open', data),
  trackLinkClick: (data) => ipcRenderer.invoke('track-link-click', data),
  getTrackingData: () => ipcRenderer.invoke('get-tracking-data'),

  // Event listeners
  onSendProgress: (callback) => ipcRenderer.on('send-progress', callback),
  onSendingPaused: (callback) => ipcRenderer.on('sending-paused', callback),
  onSendingResumed: (callback) => ipcRenderer.on('sending-resumed', callback),
  onSendingStopped: (callback) => ipcRenderer.on('sending-stopped', callback),
  onTrackingEvent: (callback) => ipcRenderer.on('tracking-event', callback)
});