<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title><PERSON><PERSON></title>
  <style>
    body {
      margin: 0;
      padding: 0;
      background-color: #ffffff;
      font-family: Arial, sans-serif;
    }
    .wrapper {
      max-width: 600px;
      margin: 0 auto;
      background-color: #d5c6f2;
      padding: 30px 20px;
      text-align: center;
      color: #333333;
    }
    h1 {
      font-size: 24px;
      color: #4b3085;
      margin-bottom: 10px;
    }
    .subtitle {
      font-size: 14px;
      color: #444;
      margin-bottom: 25px;
    }
    .stat-box {
      background-color: #ffffff;
      border-radius: 30px;
      padding: 12px 20px;
      margin: 10px auto;
      max-width: 85%;
      font-size: 14px;
      font-weight: bold;
      color: #4b3085;
    }
    .circle-note {
      display: inline-block;
      background-color: #ffffff;
      color: #4b3085;
      font-weight: bold;
      font-size: 13px;
      border-radius: 50%;
      width: 80px;
      height: 80px;
      line-height: 40px;
      padding-top: 20px;
      margin: 20px 0;
    }
    .disclaimer {
      font-size: 11px;
      color: #777777;
      margin-top: 10px;
    }
    .product-title {
      font-size: 18px;
      font-weight: bold;
      margin-top: 20px;
      color: #333333;
    }
    .tagline {
      font-size: 14px;
      color: #555555;
      margin-bottom: 30px;
    }
    .button {
      background-color: #000000;
      color: #ffffff;
      text-decoration: none;
      padding: 12px 28px;
      border-radius: 30px;
      font-weight: bold;
      font-size: 14px;
    }
    .unsubscribe {
      font-size: 12px;
      color: #777777;
      text-align: center;
      margin-top: 30px;
    }

    /* 🔧 Showerhead (CSS Simulation) */
    .showerhead {
      margin: 20px auto;
      width: 120px;
      height: 120px;
      background: radial-gradient(circle at center, #ccc 30%, #999 80%);
      border-radius: 50%;
      box-shadow: inset 0 0 10px rgba(0,0,0,0.2);
      position: relative;
    }

    .showerhead::before {
      content: "";
      position: absolute;
      top: 50%;
      left: -25px;
      transform: translateY(-50%);
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, #aaa, #777);
      border-radius: 0 50% 50% 0;
      box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
    }

    .showerhead::after {
      content: "";
      position: absolute;
      width: 6px;
      height: 6px;
      background-color: #444;
      border-radius: 50%;
      box-shadow:
        15px 15px #444,
        30px 15px #444,
        45px 15px #444,
        15px 30px #444,
        30px 30px #444,
        45px 30px #444,
        15px 45px #444,
        30px 45px #444,
        45px 45px #444;
      top: 20px;
      left: 20px;
    }

  </style>
</head>
<body>

  <div class="wrapper">
    <h1>Your Shower Water Is<br>Causing Your Hair Loss</h1>
    <div class="subtitle">
      Get softer & healthier hair with the only filter showerhead that’s clinically tested and derm approved, Lucinn.
    </div>

    <!-- 🔧 CSS Showerhead -->
    <div class="showerhead"></div>

    <div class="stat-box">93% experienced reduced Hair Shedding</div>
    <div class="stat-box">91% noticed Less Flaky Scalp & Shinier Hair</div>

    <div class="circle-note">60 Days<br>Risk Free</div>

    <div class="disclaimer">*After 4 weeks across 350 users</div>

    <div class="product-title">The Lucinn Pro Filtered Showerhead</div>
    <div class="tagline">BEAUTY STARTS WITH CLEAN WATER.</div>

    <a href="https://2ly.link/27KyL" class="button">GET NOW</a>
  </div>

  <div class="unsubscribe">
    <a href="https://2ly.link/27KyI" style="color: #777777; text-decoration: none;">Unsubscribe</a>
  </div>

</body>
</html>
