import os
import tkinter as tk
from tkinter import messagebox
import threading
import time
import base64
import json
import re
import random
import string
import sys
from google_auth_oauthlib.flow import InstalledAppFlow
from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from datetime import datetime
from tkinterweb import HtmlFrame
from tkinter import filedialog
from email.header import Header

send_thread = None
sending_paused = threading.Event()
sending_stopped = threading.Event()
sending_paused.set()  # Start in a paused state

# Directory paths
ACCOUNT_FILE = "accounts.txt"
LISTS_DIR = "lists"
SCOPES = ["https://www.googleapis.com/auth/gmail.send"]
HISTORY_DIR = "send_history"

# Ensure the history directory exists
if not os.path.exists(HISTORY_DIR):
    os.makedirs(HISTORY_DIR)

# Add this function to load the previously saved email send history
def load_send_history():
    file_path = filedialog.askopenfilename(title="Select a history file", 
                                            filetypes=[("JSON Files", "*.json")])
    if file_path:
        try:
            with open(file_path, 'r') as history_file:
                history_data = json.load(history_file)
                
                # Populate the fields with the loaded data
                from_entry.delete(0, tk.END)
                from_entry.insert(0, history_data.get("from", ""))
                
                subject_entry.delete(0, tk.END)
                subject_entry.insert(0, history_data.get("subject", ""))
                
                headers_text.delete("1.0", tk.END)
                headers_text.insert("1.0", json.dumps(history_data.get("headers", {}), indent=4))
                
                body_text.delete("1.0", tk.END)
                body_text.insert("1.0", history_data.get("body", ""))
                
                # Load selected accounts
                selected_accounts = history_data.get("servers", [])
                account_listbox.selection_clear(0, tk.END)  # Clear previous selections
                for account in selected_accounts:
                    if account in account_listbox.get(0, tk.END):
                        index = account_listbox.get(0, tk.END).index(account)
                        account_listbox.selection_set(index)

                # Load selected lists
                selected_lists = history_data.get("selected_lists", [])
                list_listbox.selection_clear(0, tk.END)  # Clear previous selections
                available_lists = [list_listbox.get(i) for i in range(list_listbox.size())]

                for list_name in selected_lists:
                    list_base_name = list_name.strip()  # Clean up whitespace
                    for available_list in available_lists:
                        base_name = available_list.split(' (')[0]  # Get the name part before '('

                        if list_base_name == base_name:  # Check for equality
                            index = available_lists.index(available_list)  # Find index of matched item
                            list_listbox.selection_set(index)  # Set selection
                            break  # Exit the loop when found

                # Load email range
                email_range = history_data.get("email_range", "")
                if email_range:
                    start, end = email_range.split('-')
                    email_start_entry.delete(0, tk.END)
                    email_start_entry.insert(0, start)
                    email_end_entry.delete(0, tk.END)
                    email_end_entry.insert(0, end)

                # Load time interval
                time_interval = history_data.get("time_interval", "")
                time_interval_entry.delete(0, tk.END)
                if time_interval:
                    time_interval_entry.insert(0, time_interval)

                # Load test emails
                test_emails = history_data.get("test_emails", "")
                test_email_text.delete("1.0", tk.END)
                test_email_text.insert("1.0", test_emails)

                # Load progress - Assuming it's just a string to display
                progress = history_data.get("progress", "")
                progress_label.config(text=progress)

        except Exception as e:
            messagebox.showerror("Error", f"An error occurred while loading the history file: {e}")


import requests

import os
import tkinter as tk
from tkinter import messagebox
from google_auth_oauthlib.flow import InstalledAppFlow
from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request
from googleapiclient.discovery import build
import requests
import webbrowser
import time

# Directory paths
SCOPES = ["https://www.googleapis.com/auth/gmail.send"]

def authenticate_gmail(account):
    creds = None
    token_file = f"token_{account}.json"  # Fichier contenant le jeton pour le compte

    # Vérification si un jeton existe déjà
    if os.path.exists(token_file):
        creds = Credentials.from_authorized_user_file(token_file, SCOPES)

    # Si les credentials ne sont pas valides ou n'existent pas
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            # Si le jeton a expiré mais peut être rafraîchi, on le rafraîchit
            print(f"Le jeton pour {account} est expiré, on tente de le rafraîchir...")
            creds.refresh(Request())
        else:
            # Sinon, on demande une nouvelle authentification
            print(f"Aucun jeton valide trouvé pour {account}, demande d'autorisation via le navigateur...")

            # Lancement du processus de requête d'autorisation
            flow = InstalledAppFlow.from_client_secrets_file(
                'client_secret_314820171334_6l6r2dkiehlp1j3ffbu0mkiafolem7ld_apps.json', SCOPES)

            # Autoriser via serveur local (Ouvrira le navigateur pour demander l'autorisation)
            creds = flow.run_local_server(port=49262)

            # Sauvegarde du nouveau jeton pour les prochaines utilisations
            with open(token_file, 'w') as token:
                token.write(creds.to_json())
                print(f"Jeton enregistré dans {token_file}")

            # Déconnecter le compte après authentification
            try:
                # Déconnexion en accédant à l'URL de déconnexion
                logout_url = 'https://accounts.google.com/Logout'
                webbrowser.open_new(logout_url)
                time.sleep(5)  # Pause de quelques secondes pour laisser le temps à la déconnexion de se faire
                print("Déconnexion réussie.")
            except Exception as e:
                print(f"Erreur lors de la déconnexion: {e}")

    # Création de l'objet service Gmail
    try:
        service = build('gmail', 'v1', credentials=creds)
        print(f"Service Gmail créé avec succès pour {account}")
        return service
    except Exception as e:
        print(f"Erreur lors de la création du service Gmail pour {account}: {e}")
        return None

def create_message(sender, to, subject, body, headers):
    # Replace tags in the sender name before encoding
    sender_name = replace_tags(from_entry.get(), sender)
    
    # Encode sender name with UTF-8 after tag replacement
    encoded_sender_name = Header(sender_name, 'utf-8').encode()  
    message = MIMEMultipart()
    message["from"] = f"{encoded_sender_name} <{sender}>"
    message["to"] = to

    # Replace tags in the subject and body
    subject = replace_tags(subject, sender)
    body = replace_tags(body, sender)
    message["subject"] = subject

    # Add any custom headers after tag replacement
    for key, value in headers.items():
        message[key] = replace_tags(value, sender)

    # Attach the body as HTML content
    msg_body = MIMEText(body, 'html')
    message.attach(msg_body)

    # Return the raw encoded message
    raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode()
    return raw_message


def send_email(service, sender, to, subject, body, headers):
    subject = replace_tags(subject, sender)
    body = replace_tags(body, sender)
    headers = {k: replace_tags(v, sender) for k, v in headers.items()}

    try:
        raw_message = create_message(sender, to, subject, body, headers)
        message = service.users().messages().send(userId="me", body={"raw": raw_message}).execute()
        print(f'"{sender}" ===> {to}')
    except HttpError as error:
        print(f"An error occurred: {error}")
        raise error

def parse_tag(tag):
    # Handle a tag like [an_x] where x is a number
    if tag.startswith("[an_"):
        try:
            # Extract the number after "an_" and generate a random alphanumeric string of that length
            length = int(tag.strip("[]").split("_")[1])
            return ''.join(random.choices(string.ascii_letters + string.digits, k=length))
        except (IndexError, ValueError):
            return "[invalid_an_tag]"  # Handle invalid tags gracefully
    
    # Handle other tags like [random_string], [current_date], etc.
    elif tag == "[random_string]":
        return ''.join(random.choices(string.ascii_letters + string.digits, k=8))
    
    elif tag == "[current_date]":
        return datetime.now().strftime("%Y-%m-%d")
    
    elif tag.startswith("[number_"):
        try:
            max_value = int(tag.strip("[]").split("_")[1])
            return str(random.randint(1, max_value))
        except (IndexError, ValueError):
            return "[invalid_number_tag]"

    # Default case for unknown tags
    return f"[unknown_tag:{tag}]"

def send_test_emails():
    selected_accounts = [account_listbox.get(i) for i in account_listbox.curselection()]
    test_emails = [email.strip() for email in test_email_text.get("1.0", tk.END).strip().split(",") if email.strip()]

    if not selected_accounts or not test_emails:
        messagebox.showerror("Error", "Please select at least one Gmail account and enter at least one test email address.")
        return

    subject = subject_entry.get()
    headers = {}
    header_text = headers_text.get("1.0", tk.END).strip()
    for line in header_text.splitlines():
        if ":" in line:
            key, value = line.split(":", 1)
            headers[key.strip()] = value.strip()

    body = body_text.get("1.0", tk.END).strip()
    if not body:
        messagebox.showerror("Error", "Please enter the email body.")
        return

    for account in selected_accounts:
        try:
            service = authenticate_gmail(account)
            if service:
                for test_email in test_emails:
                    send_email(service, account, test_email, subject, body, headers)
                messagebox.showinfo("Success", f"Test email(s) sent from {account} to {', '.join(test_emails)}.")
        except Exception as e:
            messagebox.showerror("Error", f"An error occurred while sending test emails from {account}: {e}")

def replace_tags(text, sender=None):
    # Define a pattern to match placeholders
    tag_pattern = re.compile(r'\[([a-z]+(_\d+(_\d+)?)?)\]')

    # Find all matches in the text
    tags = tag_pattern.findall(text)

    # Replace each match with the corresponding generated value
    for tag in tags:
        full_tag = f"[{tag[0]}]"
        if full_tag == "[sender]" and sender:
            replacement = sender
        else:
            replacement = parse_tag(full_tag)
        text = text.replace(full_tag, replacement)

    return text

def send_email(service, sender, to, subject, body, headers):
    # Replace tags in subject, body, and headers
    subject = replace_tags(subject, sender)
    body = replace_tags(body, sender)
    headers = {k: replace_tags(v, sender) for k, v in headers.items()}

    try:
        raw_message = create_message(sender, to, subject, body, headers)
        message = service.users().messages().send(userId="me", body={"raw": raw_message}).execute()
        print(f'"{sender}" ===> {to}')
    except HttpError as error:
        print(f"An error occurred: {error}")
        raise error

def refresh_accounts():
    account_listbox.delete(0, tk.END)
    if os.path.exists(ACCOUNT_FILE):
        with open(ACCOUNT_FILE, "r") as f:
            accounts = [line.strip().split(":")[0] for line in f if line.strip()]
            for account in accounts:
                account_listbox.insert(tk.END, account)

def refresh_lists():
    list_listbox.delete(0, tk.END)
    global email_lists
    email_lists = {}
    for filename in os.listdir(LISTS_DIR):
        filepath = os.path.join(LISTS_DIR, filename)
        with open(filepath, "r") as f:
            emails = [line.strip() for line in f if line.strip()]
            email_lists[filename] = emails
            list_listbox.insert(tk.END, f"{filename} ({len(emails)} emails)")

def send_test_emails():
    selected_accounts = [account_listbox.get(i) for i in account_listbox.curselection()]
    test_emails = [email.strip() for email in test_email_text.get("1.0", tk.END).strip().split(",") if email.strip()]

    if not selected_accounts or not test_emails:
        messagebox.showerror("Error", "Please select at least one Gmail account and enter at least one test email address.")
        return

    subject = subject_entry.get()
    headers = {}
    header_text = headers_text.get("1.0", tk.END).strip()
    for line in header_text.splitlines():
        if ":" in line:
            key, value = line.split(":", 1)
            headers[key.strip()] = value.strip()

    body = body_text.get("1.0", tk.END).strip()
    if not body:
        messagebox.showerror("Error", "Please enter the email body.")
        return

    # Save test email history with test_emails included
    save_test_email_history(subject, body, selected_accounts, headers, test_emails)  # Pass test_emails here
    
    for account in selected_accounts:
        try:
            service = authenticate_gmail(account)
            for test_email in test_emails:
                send_email(service, account, test_email, subject, body, headers)
            messagebox.showinfo("Success", f"Test email(s) sent from {account} to {', '.join(test_emails)}.")
        except Exception as e:
            messagebox.showerror("Error", f"An error occurred while sending test emails from {account}: {e}")

def send_emails(selected_accounts, selected_lists, email_start, email_end, time_interval, time_unit, subject, headers, body, start_time):
    global sending_paused, sending_stopped

    total_emails = 0
    for list_name in selected_lists:
        emails = email_lists.get(list_name, [])
        if not emails:
            messagebox.showerror("Error", f"List '{list_name}' is empty or not found.")
            continue
        total_emails += len(emails[email_start - 1:email_end])

    sent_emails = 0

    def update_progress():
        progress_label.config(text=f"{sent_emails}/{total_emails}")
        root.update_idletasks()  # Ensure the GUI is updated immediately

    def save_history(process_status):
        progress_value = f"{sent_emails}/{total_emails}"  # Ensure progress is calculated
        save_send_drop_history(selected_accounts, from_entry.get(), subject, selected_lists, headers, body, time_interval, time_unit, start_time, f"{email_start}-{email_end}", process_status, progress_value)
    time_multipliers = {
        'Milliseconds': 0.001,
        'Seconds': 1,
        'Minutes': 60,
        'Hours': 3600
    }
    
    time_multiplier = time_multipliers.get(time_unit, 1)

    for list_name in selected_lists:
        emails = email_lists.get(list_name, [])
        if not emails:
            continue

        emails = emails[email_start - 1:email_end]
        account_cycle = iter(selected_accounts)
        for email in emails:
            if sending_stopped.is_set():
                print("Process ended")  # Print status message
                messagebox.showinfo("Stopped", "Email sending process stopped.")
                root.after(0, save_history, "Stopped")  # Schedule history saving in the main thread
                sending_stopped.clear()  # Reset the sending_stopped event
                return  # Exit the function to stop further processing

            while not sending_paused.is_set():
                time.sleep(0.1)  # Wait while paused
            
            try:
                account = next(account_cycle)
            except StopIteration:
                account_cycle = iter(selected_accounts)
                account = next(account_cycle)
            try:
                service = authenticate_gmail(account)
                send_email(service, account, email, subject, body, headers)
                sent_emails += 1
                root.after(0, update_progress)  # Schedule the progress update in the main thread
                root.after(0, save_history, "InProgress")  # Schedule history saving in the main thread
                time.sleep(time_interval * time_multiplier)
            except Exception as e:
                messagebox.showerror("Error", f"An error occurred while sending emails: {e}")

    print("Process ended")  # Print status message
    messagebox.showinfo("Success", "Emails have been sent successfully.")
    root.after(0, save_history, "Completed")  # Schedule history saving in the main thread
    sending_stopped.clear()  # Reset the sending_stopped event

def background_send_drop():
    global send_thread, sending_paused, sending_stopped

    if send_thread and send_thread.is_alive():
        messagebox.showerror("Error", "A send drop is already in progress. Please stop the current send drop before starting a new one.")
        return

    selected_accounts = [account_listbox.get(i) for i in account_listbox.curselection()]
    selected_lists = [list_listbox.get(i).rsplit(' ', 2)[0] for i in list_listbox.curselection()]

    if not selected_accounts or not selected_lists:
        messagebox.showerror("Error", "Please select at least one Gmail account and one email list.")
        return

    email_start = int(email_start_entry.get()) if email_start_entry.get().isdigit() else 1
    email_end = int(email_end_entry.get()) if email_end_entry.get().isdigit() else None
    time_interval = int(time_interval_entry.get()) if time_interval_entry.get().isdigit() else 1
    time_unit = time_unit_var.get()

    subject = subject_entry.get()
    headers = {}
    header_text = headers_text.get("1.0", tk.END).strip()
    for line in header_text.splitlines():
        if ":" in line:
            key, value = line.split(":", 1)
            headers[key.strip()] = value.strip()

    body = body_text.get("1.0", tk.END).strip()
    if not body:
        messagebox.showerror("Error", "Please enter the email body.")
        return

    start_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    process_status = "Started"  # Define the process status
    progress = "0/0"  # Define the initial progress

    # Save send drop history with initial process status "Started" and progress "0/0"
    save_send_drop_history(selected_accounts, from_entry.get(), subject, selected_lists, headers, body, time_interval, time_unit, start_time, f"{email_start}-{email_end}", process_status, progress)

    # Start sending emails in a new thread
    send_thread = threading.Thread(target=send_emails, args=(selected_accounts, selected_lists, email_start, email_end, time_interval, time_unit, subject, headers, body, start_time))
    send_thread.start()
def pause_sending():
    global sending_paused
    sending_paused.clear()  # Unset the event to pause sending
    print("Send paused")  # Print message when sending is paused

def resume_sending():
    global sending_paused
    if sending_stopped.is_set():
        messagebox.showerror("Error", "The send drop has been stopped. Please start a new send drop.")
        return
    sending_paused.set()  # Set the event to resume sending
    print("Send resumed")  # Print message when sending is resumed

def stop_sending():
    global sending_stopped, send_thread
    sending_stopped.set()  # Set the event to stop sending
    progress_label.config(text="0/0")  # Reset the progress label
    print("Process ended")  # Print message when sending is stopped
    
    if send_thread and send_thread.is_alive():
        # Optionally, wait for the thread to finish
        send_thread.join(timeout=1)  # Wait for up to 1 second for the thread to finish
        if send_thread.is_alive():
            print("Thread did not terminate gracefully, forcefully terminating.")
        send_thread = None  # Reset the send_thread variable
    
    sending_stopped.clear()  # Clear the event to allow starting a new send drop

def save_test_email_history(subject, body, selected_accounts, headers, test_emails):
    # Ensure test_history directory exists
    if not os.path.exists("test_history"):
        os.makedirs("test_history")
    
    # Define the filename based on the current timestamp
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    history_filename = os.path.join("test_history", f"test_email_history_{timestamp}.json")

    # Prepare the data to save
    history_data = {
        "subject": subject,
        "body": body,
        "servers": selected_accounts,
        "headers": headers,
        "test_emails": test_emails  # Save the test emails
    }

    # Save to JSON file
    with open(history_filename, "w") as history_file:
        json.dump(history_data, history_file, indent=4)

def save_send_drop_history(servers,sender_name,subject, selected_lists, headers, body, time_interval, time_unit, start_time, email_range, process_status, progress):
    history_data = {
        "servers": servers,
        "selected_lists": selected_lists,
        "headers": headers,
        "body": body,
        "time_interval": time_interval,
        "time_unit": time_unit,
        "start_time": start_time,
        "test_emails": test_email_text.get("1.0", tk.END).strip(),
        "email_range": email_range,
        "process_status": process_status,  # Add process status
        "progress": progress,  # Add progress
        "from": sender_name,
        "subject": subject,
        
    }

    history_filename = os.path.join(HISTORY_DIR, f"send_history_{start_time.replace(':', '-')}.json")
    with open(history_filename, "w") as history_file:
        json.dump(history_data, history_file, indent=4)

def select_all_accounts():
    account_listbox.select_set(0, tk.END)
    update_selected_accounts_label()

def deselect_all_accounts():
    account_listbox.selection_clear(0, tk.END)
    update_selected_accounts_label()

def select_all_lists():
    list_listbox.select_set(0, tk.END)
    update_selected_lists_label()

def deselect_all_lists():
    list_listbox.selection_clear(0, tk.END)
    update_selected_lists_label()

def display_body_as_html():
    body_html = body_text.get("1.0", tk.END)
    if not body_html.strip():
        messagebox.showerror("Error", "Body text is empty.")
        return

    html_window = tk.Toplevel(root)
    html_window.title("Email Body Preview")
    
    frame = HtmlFrame(html_window, horizontal_scrollbar="auto")
    frame.add_html(body_html)  # Use add_html to set the HTML content
    frame.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)
def update_selected_accounts_label():
    selected_accounts_count = len(account_listbox.curselection())
    selected_accounts_label.config(text=f"Selected Accounts: {selected_accounts_count}")

def update_selected_lists_label():
    selected_lists = [list_listbox.get(i).split(' ')[0] for i in list_listbox.curselection()]
    total_emails = sum(len(email_lists[list_listbox.get(i).rsplit(' ', 2)[0]]) for i in list_listbox.curselection())
    selected_lists_label.config(text=f"Selected Emails: {total_emails}")

def save_state():
    state = {
        "selected_accounts": [account_listbox.get(i) for i in account_listbox.curselection()],
        "selected_lists": [list_listbox.get(i).split(' ')[0] for i in list_listbox.curselection()],
        "subject": subject_entry.get(),
        "from": from_entry.get(),
        "headers": headers_text.get("1.0", tk.END).strip(),
        "body": body_text.get("1.0", tk.END).strip(),
        "test_emails": test_email_text.get("1.0", tk.END).strip(),
        "email_start": email_start_entry.get(),
        "email_end": email_end_entry.get(),
        "time_interval": time_interval_entry.get(),
        "time_unit": time_unit_var.get(),
        
    }
    with open("app_state.json", "w") as state_file:
        json.dump(state, state_file, indent=4)

def load_state():
    if os.path.exists("app_state.json"):
        with open("app_state.json", "r") as state_file:
            state = json.load(state_file)
            for i, account in enumerate(state["selected_accounts"]):
                account_listbox.selection_set(i)
            for i, list_name in enumerate(state["selected_lists"]):
                list_listbox.selection_set(i)
            subject_entry.insert(0, state["subject"])
            from_entry.insert(0, state["from"])
            headers_text.insert("1.0", state["headers"])
            body_text.insert("1.0", state["body"])
            test_email_text.insert("1.0", state["test_emails"])
            email_start_entry.insert(0, state["email_start"])
            email_end_entry.insert(0, state["email_end"])
            time_interval_entry.insert(0, state["time_interval"])
            time_unit_var.set(state["time_unit"])
            

def restart_app():
    save_state()
    python = sys.executable
    os.execl(python, python, *sys.argv)

root = tk.Tk()
root.title("Gmail API Email Sender")

# Accounts Listbox and Controls
account_frame = tk.Frame(root)
account_frame.grid(row=0, column=0, rowspan=6, padx=10, pady=10, sticky="nsew")

tk.Button(account_frame, text="Refresh Accounts", command=refresh_accounts).grid(row=0, column=0, padx=10, pady=5, sticky="w")
tk.Button(account_frame, text="Select All Accounts", command=select_all_accounts).grid(row=1, column=0, padx=10, pady=5, sticky="w")
tk.Button(account_frame, text="Deselect All Accounts", command=deselect_all_accounts).grid(row=1, column=1, padx=10, pady=5, sticky="w")
account_listbox = tk.Listbox(account_frame, selectmode=tk.MULTIPLE, exportselection=False)
account_listbox.grid(row=2, column=0, columnspan=2, padx=10, pady=5, sticky="nsew")
account_listbox.bind('<<ListboxSelect>>', lambda event: update_selected_accounts_label())

# List Listbox and Controls
list_frame = tk.Frame(root)
list_frame.grid(row=0, column=1, rowspan=6, padx=10, pady=10, sticky="nsew")

tk.Button(list_frame, text="Refresh Lists", command=refresh_lists).grid(row=0, column=0, padx=10, pady=5, sticky="w")
tk.Button(list_frame, text="Select All Lists", command=select_all_lists).grid(row=1, column=0, padx=10, pady=5, sticky="w")
tk.Button(list_frame, text="Deselect All Lists", command=deselect_all_lists).grid(row=1, column=1, padx=10, pady=5, sticky="w")
list_listbox = tk.Listbox(list_frame, selectmode=tk.MULTIPLE, exportselection=False)
list_listbox.grid(row=2, column=0, columnspan=2, padx=10, pady=5, sticky="nsew")
list_listbox.bind('<<ListboxSelect>>', lambda event: update_selected_lists_label())

# Labels to display selected counts
selected_accounts_label = tk.Label(root, text="Selected Accounts: 0")
selected_accounts_label.grid(row=6, column=0, padx=10, pady=5, sticky="w")

selected_lists_label = tk.Label(root, text="Selected Emails: 0")
selected_lists_label.grid(row=6, column=1, padx=10, pady=5, sticky="w")

#Subject Entry
tk.Label(root, text="Subject:").grid(row=0, column=2, padx=10, pady=5, sticky="w")
subject_entry = tk.Entry(root, width=60)
subject_entry.grid(row=0, column=3, padx=10, pady=5, sticky="w")

#From Entry
tk.Label(root, text="From name:").grid(row=1, column=2, padx=10, pady=5, sticky="w")
from_entry = tk.Entry(root, width=60)
from_entry.grid(row=1, column=3, padx=10, pady=5, sticky="w")

#Headers
#Text
tk.Label(root, text="Custom Headers:").grid(row=2, column=2, padx=10, pady=5, sticky="nw")
headers_text = tk.Text(root, height=4, width=60)
headers_text.grid(row=2, column=3, padx=10, pady=5, sticky="w")

#Body
#Text
tk.Label(root, text="Body:").grid(row=3, column=2, padx=10, pady=5, sticky="nw")
body_text = tk.Text(root, height=10, width=60)
body_text.grid(row=3, column=3, padx=10, pady=5, sticky="w")

#Test Email Entry
tk.Label(root, text="Test Emails (comma separated):").grid(row=4, column=2, padx=10, pady=5, sticky="w")
test_email_text = tk.Text(root, height=2, width=60)
test_email_text.grid(row=4, column=3, padx=10, pady=5, sticky="w")

#Email
#Range
tk.Label(root, text="Email Range:").grid(row=5, column=2, padx=10, pady=5, sticky="w")
email_start_entry = tk.Entry(root, width=10)
email_start_entry.grid(row=5, column=3, padx=10, pady=5, sticky="w")
tk.Label(root, text="to").grid(row=5, column=3, padx=(100, 10), pady=5, sticky="w")
email_end_entry = tk.Entry(root, width=10)
email_end_entry.grid(row=5, column=3, padx=(150, 10), pady=5, sticky="w")


tk.Label(root, text="Time Interval:").grid(row=6, column=2, padx=10, pady=5, sticky="w")
time_interval_entry = tk.Entry(root, width=10)
time_interval_entry.grid(row=6, column=3, padx=10, pady=5, sticky="w")
time_unit_var = tk.StringVar(value="Seconds")
time_unit_menu = tk.OptionMenu(root, time_unit_var, "Milliseconds", "Seconds", "Minutes", "Hours")
time_unit_menu.grid(row=6, column=3, padx=(150, 10), pady=5, sticky="w")


tk.Label(root, text="Progress:").grid(row=7, column=2, padx=10, pady=5, sticky="w")
progress_label = tk.Label(root, text="0/0")
progress_label.grid(row=7, column=3, padx=10, pady=5, sticky="w")


#for controlling the sending process
def on_pause_enter(e):
    pause_button.config(bg="black", fg="white", text="P  A  U  S  E")

def on_pause_leave(e):
    pause_button.config(bg="red", fg="black", text="Pause")

# Create button with styling
pause_button = tk.Button(
    root, 
    text="Pause", 
    command=pause_sending,
    font=("Arial", 12, "bold"),  
    bg="red",  # Default background
    fg="black",  # Default text color
    activebackground="black",  # Background when clicked
    activeforeground="white",  # Text color when clicked
    padx=15,  
    pady=5,  
    borderwidth=3,  
    relief="raised"  
)

# Apply hover effects
pause_button.bind("<Enter>", on_pause_enter)
pause_button.bind("<Leave>", on_pause_leave)

# Grid placement (same as before)
pause_button.grid(row=8, column=2, padx=10, pady=10, sticky="w")


resume_button = tk.Button(root, text="Resume", command=resume_sending)
resume_button.grid(row=8, column=3, padx=10, pady=10, sticky="w")

stop_button = tk.Button(root, text="Stop", command=stop_sending)
stop_button.grid(row=8, column=4, padx=10, pady=10, sticky="w")

send_test_button = tk.Button(root, text="Test Email", command=send_test_emails)
send_test_button.grid(row=8, column=0, padx=10, pady=10, sticky="w")

send_drop_button = tk.Button(root, text="Send Drop", command=lambda: threading.Thread(target=background_send_drop).start())
send_drop_button.grid(row=8, column=1, padx=10, pady=10, sticky="w")

display_body_button = tk.Button(root, text="Display Body", command=display_body_as_html)
display_body_button.grid(row=9, column=3, padx=10, pady=10, sticky="e")

restart_button = tk.Button(root, text="Restart App", command=restart_app)
restart_button.grid(row=9, column=0, padx=10, pady=10, sticky="w")

def on_enter(e):
    resend_process_button.config(bg="black", fg="white", text="R  E  S  E  N  D")

def on_leave(e):
    resend_process_button.config(bg="red", fg="black", text="Resend Process")

# Create button with styling
resend_process_button = tk.Button(
    root, 
    text="Resend Process", 
    command=load_send_history,
    font=("Arial", 12, "bold"),  
    bg="red",  # Default background
    fg="black",  # Default text color
    activebackground="black",  # Background when clicked
    activeforeground="white",  # Text color when clicked
    padx=15,  
    pady=5,  
    borderwidth=3,  
    relief="raised"  
)

# Apply hover effects
resend_process_button.bind("<Enter>", on_enter)
resend_process_button.bind("<Leave>", on_leave)


# Grid placement (same as before)
resend_process_button.grid(row=9, column=2, padx=10, pady=10, sticky="w")

refresh_accounts()
refresh_lists()

# Load the saved state if it exists
load_state()





root.mainloop()