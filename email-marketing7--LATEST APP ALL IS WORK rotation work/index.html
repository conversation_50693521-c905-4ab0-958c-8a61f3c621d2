<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>MH-SENDER</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-12">
        <div class="text-center mt-3 mb-4">
          <div class="app-title animate__animated animate__fadeIn">
            <h1 id="brand-title">MH-SENDER</h1>
            <p class="app-subtitle">Professional Email Marketing Platform</p>
          </div>
        </div>
        
        <!-- Status Messages -->
        <div id="success-message" class="alert alert-success" style="display: none;"></div>
        <div id="error-message" class="alert alert-danger" style="display: none;"></div>
        
        <!-- Navigation -->
        <ul class="nav nav-tabs mb-4">
          <li class="nav-item">
            <a class="nav-link active" href="#" data-tab="email-composition">
              <i class="bi bi-envelope"></i> Email Composition
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#" data-tab="accounts">
              <i class="bi bi-person"></i> Accounts
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#" data-tab="lists">
              <i class="bi bi-list-ul"></i> Email Lists
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#" data-tab="history">
              <i class="bi bi-clock-history"></i> History
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#" data-tab="dashboard">
              <i class="bi bi-graph-up"></i> Dashboard
            </a>
          </li>
        </ul>
        
        <!-- Email Composition Tab -->
        <div id="email-composition" class="tab-content">
          <div class="row">
            <div class="col-md-6">
              <div class="card mb-3">
                <div class="card-header">
                  <h5>Email Content</h5>
                </div>
                <div class="card-body">
                  <div class="mb-3">
                    <label for="from-name-input" class="form-label">From Name:</label>
                    <input type="text" class="form-control" id="from-name-input" placeholder="John Doe">
                    <small class="text-muted">
                      Name that will appear as sender. Supports tags: [sender], [random_string], [an_8], [number_100], [current_date]
                      <br>Example: "John [random_string]" or "[sender] Marketing"
                    </small>
                  </div>
                  
                  <div class="mb-3">
                    <label for="subject-input" class="form-label">Subject:</label>
                    <input type="text" class="form-control" id="subject-input" placeholder="Your email subject">
                    <small class="text-muted">Supports the same tags as From Name</small>
                  </div>
                  
                  <div class="mb-3">
                    <label for="headers-text" class="form-label">Custom Headers (JSON):</label>
                    <div class="input-group">
                      <textarea class="form-control" id="headers-text" rows="4" placeholder='{"Reply-To": "<EMAIL>", "X-Priority": "1"}'></textarea>
                      <button class="btn btn-outline-info" type="button" onclick="validateHeaders()">
                        <i class="bi bi-check-circle"></i> Validate
                      </button>
                      <div class="btn-group">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                          Presets
                        </button>
                        <ul class="dropdown-menu">
                          <li><a class="dropdown-item" href="#" onclick="setHeaderPreset('reply-to')">Reply-To</a></li>
                          <li><a class="dropdown-item" href="#" onclick="setHeaderPreset('high-priority')">High Priority</a></li>
                          <li><a class="dropdown-item" href="#" onclick="setHeaderPreset('unsubscribe')">Unsubscribe</a></li>
                          <li><a class="dropdown-item" href="#" onclick="setHeaderPreset('marketing')">Marketing Bundle</a></li>
                          <li><hr class="dropdown-divider"></li>
                          <li><a class="dropdown-item" href="#" onclick="clearHeaders()">Clear Headers</a></li>
                        </ul>
                      </div>
                    </div>
                    <small class="text-muted">
                      Optional custom email headers in JSON format. Use presets or enter manually.<br>
                      <strong>Examples:</strong> Reply-To, X-Priority, List-Unsubscribe, X-Mailer, etc.
                    </small>
                  </div>
                  
                  <div class="mb-3">
                    <label for="body-text" class="form-label">Body (HTML):</label>
                    <div class="input-group">
                      <textarea class="form-control" id="body-text" rows="10" placeholder="<p>Your HTML email content</p>"></textarea>
                      <button class="btn btn-outline-secondary" type="button" id="preview-html-btn">Preview</button>
                    </div>
                    <small class="text-muted">HTML content with tag support. Tags will be replaced with dynamic content for each email.</small>
                  </div>
                  
                  <!-- HTML Preview Modal -->
                  <div class="modal" id="html-preview-modal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                      <div class="modal-content">
                        <div class="modal-header">
                          <h5 class="modal-title">HTML Preview</h5>
                          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" id="close-preview-modal"></button>
                        </div>
                        <div class="modal-body">
                          <div id="html-preview-content" style="border: 1px solid #ddd; padding: 15px; background-color: white;"></div>
                        </div>
                        <div class="modal-footer">
                          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="close-preview-btn">Close</button>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div class="mb-3">
                    <div class="form-check form-switch">
                      <input class="form-check-input" type="checkbox" id="enable-tracking" checked>
                      <label class="form-check-label" for="enable-tracking">
                        Enable open and click tracking
                      </label>
                    </div>
                    <small class="text-muted">
                      Adds a tracking pixel to track email opens and processes links for click tracking
                    </small>
                  </div>
                </div>
              </div>

              <!-- Tag Information Card -->
              <div class="card mb-3">
                <div class="card-header">
                  <h6 class="mb-0">
                    <button class="btn btn-link text-decoration-none p-0" type="button" data-bs-toggle="collapse" data-bs-target="#tagInfoCollapse" aria-expanded="false" aria-controls="tagInfoCollapse">
                      <i class="bi bi-info-circle"></i> Available Tags (Click to expand)
                    </button>
                  </h6>
                </div>
                <div class="collapse" id="tagInfoCollapse">
                  <div class="card-body">
                    <small class="text-muted">
                      <strong>Dynamic Tags for From Name, Subject, and Body:</strong><br>
                      <code>[sender]</code> - Current sender email address<br>
                      <code>[random_string]</code> - Random 8-character string<br>
                      <code>[an_X]</code> - Random alphanumeric string of X length (e.g., [an_5])<br>
                      <code>[number_X]</code> - Random number from 1 to X (e.g., [number_100])<br>
                      <code>[current_date]</code> - Current date in YYYY-MM-DD format<br><br>
                      <strong>Example:</strong> "John [an_3] Marketing" might become "John A7k Marketing"
                    </small>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-md-6">
              <div class="card mb-3">
                <div class="card-header">
                  <h5>Sending Configuration</h5>
                </div>
                <div class="card-body">
                  <div class="mb-3">
                    <label for="client-select" class="form-label">Select OAuth Client:</label>
                    <div class="d-flex">
                      <select class="form-select me-2" id="client-select"></select>
                      <button id="upload-client-btn" class="btn btn-outline-primary me-1">
                        <i class="bi bi-upload"></i>
                      </button>
                      <button id="delete-client-btn" class="btn btn-outline-danger">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                    <small class="text-muted">Select or upload your Google OAuth client JSON file</small>
                  </div>
                  
                  <div class="mb-3">
                    <label for="account-listbox" class="form-label">Select Accounts:</label>
                    <select class="form-select" id="account-listbox" multiple size="5"></select>
                    <small class="text-muted">Use Ctrl+Click or Shift+Click to select multiple accounts</small>
                  </div>
                  
                  <div class="mb-3">
                    <label for="list-listbox" class="form-label">Select Email Lists:</label>
                    <select class="form-select" id="list-listbox" multiple size="5"></select>
                    <small class="text-muted">Use Ctrl+Click or Shift+Click to select multiple lists</small>
                  </div>
                  
                  <div class="row mb-3">
                    <div class="col-md-6">
                      <label for="email-start-input" class="form-label">Email Range (Start):</label>
                      <input type="number" class="form-control" id="email-start-input" placeholder="1">
                    </div>
                    <div class="col-md-6">
                      <label for="email-end-input" class="form-label">Email Range (End):</label>
                      <input type="number" class="form-control" id="email-end-input" placeholder="100">
                    </div>
                    <div class="col-12">
                      <small class="text-muted">Optional: Define a range of emails to send (leave empty to send all)</small>
                    </div>
                  </div>
                  
                  <div class="mb-3">
                    <label for="time-interval-input" class="form-label">Time Interval (seconds):</label>
                    <input type="number" class="form-control" id="time-interval-input" value="60">
                    <small class="text-muted">Time between emails in seconds</small>
                  </div>
                  
                  <div class="mb-3">
                    <label for="emails-per-account-input" class="form-label">Emails Per Account:</label>
                    <input type="number" class="form-control" id="emails-per-account-input" value="10">
                    <small class="text-muted">Number of emails to send from each account before rotating</small>
                  </div>
                  
                  <div class="mb-3">
                    <label for="batch-size-input" class="form-label">Batch Size:</label>
                    <input type="number" class="form-control" id="batch-size-input" value="1">
                    <small class="text-muted">Number of emails to send in one batch per account</small>
                  </div>
                  
                  <div class="mb-3">
                    <label for="test-email-interval-input" class="form-label">Test Email Interval:</label>
                    <input type="number" class="form-control" id="test-email-interval-input" value="0">
                    <small class="text-muted">Send a test email after every X emails (0 to disable)</small>
                  </div>
                </div>
              </div>
              
              <div class="card mb-3">
                <div class="card-header">
                  <h5>Test Emails</h5>
                </div>
                <div class="card-body">
                  <div class="mb-3">
                    <label for="test-email-input" class="form-label">Test Email Addresses:</label>
                    <textarea class="form-control" id="test-email-input" rows="2" placeholder="<EMAIL>, <EMAIL>"></textarea>
                    <small class="text-muted">Separate multiple emails with commas or new lines</small>
                  </div>
                  
                  <div class="mb-3">
                    <button id="send-test-btn" class="btn btn-primary">
                      <i class="bi bi-send"></i> Send Test Emails
                    </button>
                  </div>
                </div>
              </div>
              
              <div class="card mb-3">
                <div class="card-header">
                  <h5>Tracking Configuration</h5>
                </div>
                <div class="card-body">
                  <div class="mb-3">
                    <label for="tracking-server-url" class="form-label">Tracking Server URL:</label>
                    <input type="text" class="form-control" id="tracking-server-url" 
                           placeholder="https://yourserver.com/tracking">
                    <small class="text-muted">
                      Server that will handle tracking pixels and clicked links.
                      Leave blank to use the built-in tracking service.
                    </small>
                  </div>
                  <div class="mb-3">
                    <label for="tracking-campaign-id" class="form-label">Campaign ID:</label>
                    <input type="text" class="form-control" id="tracking-campaign-id" 
                           placeholder="unique-campaign-identifier">
                    <small class="text-muted">
                      Unique identifier for this campaign (used in tracking URLs)
                    </small>
                  </div>
                </div>
              </div>
              
              <div class="card mb-3">
                <div class="card-header">
                  <h5>Campaign Controls</h5>
                </div>
                <div class="card-body">
                  <div class="mb-3 d-flex gap-2">
                    <button id="start-campaign-btn" class="btn btn-success">
                      <i class="bi bi-play-fill"></i> Start Campaign
                    </button>
                    <button id="pause-campaign-btn" class="btn btn-warning" disabled>
                      <i class="bi bi-pause-fill"></i> Pause
                    </button>
                    <button id="resume-campaign-btn" class="btn btn-info" disabled>
                      <i class="bi bi-arrow-right"></i> Resume
                    </button>
                    <button id="stop-campaign-btn" class="btn btn-danger" disabled>
                      <i class="bi bi-stop-fill"></i> Stop
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Progress Section -->
          <div id="progress-container" class="card mb-3" style="display: none;">
            <div class="card-header">
              <h5>Sending Progress</h5>
            </div>
            <div class="card-body">
              <div class="progress mb-3">
                <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" style="width: 0%"></div>
              </div>
              <p id="progress-text">Waiting to start...</p>
              <div id="progress-details" class="border rounded p-2 bg-light" style="max-height: 200px; overflow-y: auto;"></div>
            </div>
          </div>
        </div>
        
        <!-- Accounts Tab -->
        <div id="accounts" class="tab-content" style="display: none;">
          <div class="row">
            <div class="col-md-6">
              <div class="card mb-3">
                <div class="card-header">
                  <h5>Add New Account</h5>
                </div>
                <div class="card-body">
                  <div class="mb-3">
                    <label for="new-account-input" class="form-label">Email Address:</label>
                    <div class="input-group">
                      <input type="email" class="form-control" id="new-account-input" placeholder="<EMAIL>">
                      <button id="add-account-btn" class="btn btn-primary">
                        <i class="bi bi-plus"></i> Add
                      </button>
                    </div>
                    <small class="text-muted">Enter a Gmail account to add</small>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="col-md-6">
              <div class="card mb-3">
                <div class="card-header">
                  <h5>Manage Accounts</h5>
                </div>
                <div class="card-body">
                  <div class="mb-3">
                    <label for="manage-account-listbox" class="form-label">Select accounts to manage:</label>
                    <select class="form-select mb-2" id="manage-account-listbox" multiple size="10"></select>
                    <small class="text-muted">Use Ctrl+Click or Shift+Click to select multiple accounts</small>
                    <div class="mt-2">
                      <button id="delete-account-btn" class="btn btn-danger">
                        <i class="bi bi-trash"></i> Delete Selected
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Lists Tab -->
        <div id="lists" class="tab-content" style="display: none;">
          <div class="row">
            <div class="col-md-12">
              <div class="card mb-3">
                <div class="card-header">
                  <h5>Manage Email Lists</h5>
                </div>
                <div class="card-body">
                  <div class="mb-3">
                    <button id="create-list-btn" class="btn btn-primary mb-3">
                      <i class="bi bi-plus"></i> Create New List
                    </button>
                    
                    <label for="manage-list-listbox" class="form-label">Email Lists:</label>
                    <select class="form-select mb-2" id="manage-list-listbox" multiple size="10"></select>
                    <small class="text-muted">Use Ctrl+Click or Shift+Click to select multiple lists</small>
                    
                    <div class="mt-2">
                      <button id="delete-list-btn" class="btn btn-danger">
                        <i class="bi bi-trash"></i> Delete Selected
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- History Tab -->
        <div id="history" class="tab-content" style="display: none;">
          <div class="row">
            <div class="col-md-12">
              <div class="card mb-3">
                <div class="card-header">
                  <h5>Campaign History</h5>
                </div>
                <div class="card-body">
                  <div class="mb-3 d-flex gap-2">
                    <button id="save-history-btn" class="btn btn-primary">
                      <i class="bi bi-save"></i> Save Current Settings
                    </button>
                    
                    <button id="load-history-btn" class="btn btn-secondary">
                      <i class="bi bi-folder-open"></i> Load History
                    </button>
                  </div>
                  
                  <p class="text-muted">
                    Save your current campaign settings or load previously saved campaigns.
                    This includes subject, body, selected accounts, email lists, and other configuration.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Dashboard Tab -->
        <div id="dashboard" class="tab-content" style="display: none;">
          <div class="row mb-4">
            <div class="col-md-12">
              <div class="card">
                <div class="card-header">
                  <h5>Campaign Performance Dashboard</h5>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-3 mb-3">
                      <div class="card bg-primary text-white">
                        <div class="card-body py-3 text-center">
                          <h6 class="mb-0">Total Emails</h6>
                          <h2 id="dashboard-total-emails" class="mt-2 mb-0">0</h2>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-3 mb-3">
                      <div class="card bg-success text-white">
                        <div class="card-body py-3 text-center">
                          <h6 class="mb-0">Sent Successfully</h6>
                          <h2 id="dashboard-success" class="mt-2 mb-0">0</h2>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-3 mb-3">
                      <div class="card bg-danger text-white">
                        <div class="card-body py-3 text-center">
                          <h6 class="mb-0">Errors</h6>
                          <h2 id="dashboard-errors" class="mt-2 mb-0">0</h2>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-3 mb-3">
                      <div class="card bg-info text-white">
                        <div class="card-body py-3 text-center">
                          <h6 class="mb-0">Success Rate</h6>
                          <h2 id="dashboard-success-rate" class="mt-2 mb-0">0%</h2>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="row mb-4">
            <div class="col-md-6">
              <div class="card h-100">
                <div class="card-header">
                  <h5>Sending Rate</h5>
                </div>
                <div class="card-body">
                  <canvas id="sending-rate-chart" height="250"></canvas>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="card h-100">
                <div class="card-header">
                  <h5>Success vs Errors</h5>
                </div>
                <div class="card-body">
                  <canvas id="success-error-chart" height="250"></canvas>
                </div>
              </div>
            </div>
          </div>
          
          <div class="row mb-4">
            <div class="col-md-12">
              <div class="card">
                <div class="card-header">
                  <h5>Account Performance</h5>
                </div>
                <div class="card-body">
                  <div class="table-responsive">
                    <table class="table table-striped">
                      <thead>
                        <tr>
                          <th>Account</th>
                          <th>Emails Sent</th>
                          <th>Success</th>
                          <th>Errors</th>
                          <th>Success Rate</th>
                          <th>Status</th>
                        </tr>
                      </thead>
                      <tbody id="account-performance-table">
                        <!-- Account rows will be added dynamically -->
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="row mb-4">
            <div class="col-md-12">
              <div class="card">
                <div class="card-header">
                  <h5>Recent Activity</h5>
                </div>
                <div class="card-body">
                  <div id="recent-activity-log" class="border rounded p-2 bg-light" style="max-height: 200px; overflow-y: auto;">
                    <!-- Activity logs will be added dynamically -->
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="row">
            <div class="col-md-12">
              <div class="card">
                <div class="card-header">
                  <h5>Email Engagement Tracking</h5>
                </div>
                <div class="card-body">
                  <ul class="nav nav-tabs mb-3" id="tracking-tabs" role="tablist">
                    <li class="nav-item" role="presentation">
                      <button class="nav-link active" id="opens-tab" data-bs-toggle="tab" data-bs-target="#opens-content" type="button" role="tab" aria-controls="opens-content" aria-selected="true">
                        Opens
                      </button>
                    </li>
                    <li class="nav-item" role="presentation">
                      <button class="nav-link" id="clicks-tab" data-bs-toggle="tab" data-bs-target="#clicks-content" type="button" role="tab" aria-controls="clicks-content" aria-selected="false">
                        Clicks
                      </button>
                    </li>
                  </ul>
                  
                  <div class="tab-content" id="tracking-tab-content">
                    <div class="tab-pane fade show active" id="opens-content" role="tabpanel" aria-labelledby="opens-tab">
                      <div class="row">
                        <div class="col-md-4">
                          <div class="card bg-light">
                            <div class="card-body text-center">
                              <h3 id="total-opens-count">0</h3>
                              <p class="mb-0">Total Opens</p>
                            </div>
                          </div>
                        </div>
                        <div class="col-md-4">
                          <div class="card bg-light">
                            <div class="card-body text-center">
                              <h3 id="unique-opens-count">0</h3>
                              <p class="mb-0">Unique Opens</p>
                            </div>
                          </div>
                        </div>
                        <div class="col-md-4">
                          <div class="card bg-light">
                            <div class="card-body text-center">
                              <h3 id="open-rate-percentage">0%</h3>
                              <p class="mb-0">Open Rate</p>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div class="mt-4">
                        <canvas id="opens-chart" height="250"></canvas>
                      </div>
                      
                      <div class="mt-4">
                        <h6>Recent Opens</h6>
                        <div class="table-responsive">
                          <table class="table table-sm table-striped">
                            <thead>
                              <tr>
                                <th>Email</th>
                                <th>Time</th>
                                <th>Device</th>
                                <th>IP Address</th>
                              </tr>
                            </thead>
                            <tbody id="opens-table-body">
                              <!-- Opens data will be added dynamically -->
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                    
                    <div class="tab-pane fade" id="clicks-content" role="tabpanel" aria-labelledby="clicks-tab">
                      <div class="row">
                        <div class="col-md-4">
                          <div class="card bg-light">
                            <div class="card-body text-center">
                              <h3 id="total-clicks-count">0</h3>
                              <p class="mb-0">Total Clicks</p>
                            </div>
                          </div>
                        </div>
                        <div class="col-md-4">
                          <div class="card bg-light">
                            <div class="card-body text-center">
                              <h3 id="unique-clicks-count">0</h3>
                              <p class="mb-0">Unique Clicks</p>
                            </div>
                          </div>
                        </div>
                        <div class="col-md-4">
                          <div class="card bg-light">
                            <div class="card-body text-center">
                              <h3 id="click-rate-percentage">0%</h3>
                              <p class="mb-0">Click Rate</p>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div class="mt-4">
                        <canvas id="clicks-chart" height="250"></canvas>
                      </div>
                      
                      <div class="mt-4">
                        <h6>Popular Links</h6>
                        <div class="table-responsive">
                          <table class="table table-sm table-striped">
                            <thead>
                              <tr>
                                <th>Link</th>
                                <th>Clicks</th>
                                <th>Unique Clicks</th>
                                <th>Last Clicked</th>
                              </tr>
                            </thead>
                            <tbody id="links-table-body">
                              <!-- Links data will be added dynamically -->
                            </tbody>
                          </table>
                        </div>
                      </div>
                      
                      <div class="mt-4">
                        <h6>Recent Clicks</h6>
                        <div class="table-responsive">
                          <table class="table table-sm table-striped">
                            <thead>
                              <tr>
                                <th>Email</th>
                                <th>Link</th>
                                <th>Time</th>
                                <th>Device</th>
                              </tr>
                            </thead>
                            <tbody id="clicks-table-body">
                              <!-- Clicks data will be added dynamically -->
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Create List Modal -->
  <div id="create-list-modal" class="modal">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Create Email List</h5>
          <button type="button" class="btn-close" onclick="hideCreateListModal()"></button>
        </div>
        <div class="modal-body">
          <div class="mb-3">
            <label for="list-name-input" class="form-label">List Name:</label>
            <input type="text" class="form-control" id="list-name-input" placeholder="My Email List">
          </div>
          
          <div class="mb-3">
            <label for="list-emails-input" class="form-label">Email Addresses:</label>
            <textarea class="form-control" id="list-emails-input" rows="10" placeholder="<EMAIL>&#10;<EMAIL>&#10;<EMAIL>"></textarea>
            <small class="text-muted">Enter one email per line, or separate with commas/semicolons</small>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" onclick="hideCreateListModal()">Cancel</button>
          <button type="button" class="btn btn-primary" id="save-list-btn">Save List</button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Template Tags Help Modal -->
  <div class="position-fixed bottom-0 end-0 p-3">
    <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#tagsHelpModal">
      <i class="bi bi-question-circle"></i> Template Tags Help
    </button>
  </div>
  
  <div class="modal fade" id="tagsHelpModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Template Tags Reference</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <p>You can use the following tags in your email subject, body, and headers:</p>
          
          <table class="table table-striped">
            <thead>
              <tr>
                <th>Tag</th>
                <th>Description</th>
                <th>Example Output</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>[random_string]</code></td>
                <td>Generates a random 8-character string</td>
                <td>a7f3d9c2</td>
              </tr>
              <tr>
                <td><code>[current_date]</code></td>
                <td>Inserts the current date</td>
                <td>2023-11-15</td>
              </tr>
              <tr>
                <td><code>[an_X]</code></td>
                <td>Generates a random alphanumeric string of length X (replace X with a number)</td>
                <td>[an_5] → 7Fh3k</td>
              </tr>
              <tr>
                <td><code>[number_X]</code></td>
                <td>Generates a random number between 1 and X (replace X with a number)</td>
                <td>[number_100] → 42</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Bootstrap JavaScript Bundle with Popper -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
  
  <!-- Chart.js for Dashboard -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  
  <!-- Application Script -->
  <script src="renderer.js"></script>
</body>
</html>