# Return-Path Issue & Solution

## 🚨 The Problem You Found

When using Gmail API, you're getting **TWO Return-Path headers**:

```
Return-Path: <<EMAIL>>    ← Gmail's automatic one
Return-Path: <EMAIL>            ← Your custom one
```

## 🔍 Why This Happens

Gmail API **automatically** adds a Return-Path header based on the authenticated sender account. You **cannot override or remove** this system-generated header.

## ✅ The Solution

Instead of using `Return-Path`, use alternative headers that work better with Gmail API:

### Use This Instead:
```json
{
  "Errors-To": "<EMAIL>",
  "Return-Receipt-To": "<EMAIL>"
}
```

### Why This Works:
- ✅ No conflicts with Gmail's automatic headers
- ✅ Many mail servers respect `Errors-To` for bounces
- ✅ `Return-Receipt-To` is used for delivery notifications
- ✅ You get only ONE of each header (no duplicates)

## 🎯 How to Use in Your App

1. **Use the New Preset**: 
   - Click "Presets" → "Bounce Handling (Gmail API)"
   - This automatically adds the correct headers

2. **Manual Entry**:
```json
{
  "Reply-To": "<EMAIL>",
  "Errors-To": "<EMAIL>",
  "Return-Receipt-To": "<EMAIL>"
}
```

## 📧 What You'll See in Email Source

**Before (with duplicate Return-Path):**
```
Return-Path: <<EMAIL>>
Return-Path: <EMAIL>          ← Duplicate!
```

**After (with alternative headers):**
```
Return-Path: <<EMAIL>>  ← Only Gmail's one
Errors-To: <EMAIL>         ← Your bounce handler
Return-Receipt-To: <EMAIL> ← Your receipt handler
```

## 🧪 Testing

1. Use the "Bounce Handling (Gmail API)" preset
2. Send test email to invalid address: `<EMAIL>`
3. Check both:
   - Your Gmail account (<EMAIL>)
   - Your custom bounce address (<EMAIL>)

## 📝 Summary

- **Problem**: Gmail API creates duplicate Return-Path headers
- **Solution**: Use `Errors-To` and `Return-Receipt-To` instead
- **Result**: Clean headers with proper bounce handling
- **Action**: Use the "Bounce Handling (Gmail API)" preset in your app

This solution gives you the bounce handling functionality you want without the duplicate header issue!
